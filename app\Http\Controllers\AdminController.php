<?php
// app/Http/Controllers/AdminController.php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Absensi;
use App\Models\User;
use App\Models\IzinCuti;
use App\Models\Setting;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function dashboard(Request $request)
    {
        try {
            // Statistik Pengguna
            $userCount = User::count();
            $userActive = User::where('role', 'user')->count();

            // Statistik Absensi
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));

            // Ambil bulan dan tahun dari request atau gunakan bulan dan tahun saat ini
            $currentMonth = $request->input('month', date('m'));
            $currentYear = $request->input('year', date('Y'));

            // Absensi hari ini
            $absensiToday = Absensi::whereDate('tanggal', $today)
                                ->where('status', 'hadir')
                                ->count();



            // Absensi bulan ini
            $absensiMonth = Absensi::whereMonth('tanggal', $currentMonth)
                                ->whereYear('tanggal', $currentYear)
                                ->where('status', 'hadir')
                                ->count();

            // Hitung jumlah pengajuan berdasarkan status
            $izinPending = Absensi::whereIn('status', ['izin', 'sakit', 'cuti'])
                            ->where('status_approval', 'pending')
                            ->count();

            $izinApproved = Absensi::whereIn('status', ['izin', 'sakit', 'cuti'])
                            ->where('status_approval', 'approved')
                            ->count();

            $izinRejected = Absensi::whereIn('status', ['izin', 'sakit', 'cuti'])
                            ->where('status_approval', 'rejected')
                            ->count();

            // Statistik keterlambatan bulan ini
            $terlambatBulanIni = 0;
            $tepatWaktuBulanIni = 0;

            try {
                $jamMasuk = Setting::getValue('jam_masuk', '08:00:00');
                $toleransiKeterlambatan = (int)Setting::getValue('toleransi_keterlambatan', '15');

                $absensiHadirBulanIni = Absensi::whereMonth('tanggal', $currentMonth)
                                        ->whereYear('tanggal', $currentYear)
                                        ->where('status', 'hadir')
                                        ->get();

                foreach ($absensiHadirBulanIni as $absen) {
                    try {
                        $jamMasukDateTime = Carbon::createFromFormat('H:i:s', $jamMasuk);
                        $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);
                        $jamMasukUser = Carbon::parse($absen->jam_masuk);

                        if ($jamMasukUser->gt($batasKeterlambatan)) {
                            $terlambatBulanIni++;
                        } else {
                            $tepatWaktuBulanIni++;
                        }
                    } catch (\Exception $e) {
                        // Skip jika ada error parsing
                        continue;
                    }
                }
            } catch (\Exception $e) {
                // Jika terjadi error, tetapkan nilai default
                $terlambatBulanIni = 0;
                $tepatWaktuBulanIni = 0;
            }

            // Statistik izin/cuti/sakit bulan ini
            $izinBulanIni = Absensi::whereMonth('tanggal', $currentMonth)
                            ->whereYear('tanggal', $currentYear)
                            ->where('status', 'izin')
                            ->count();

            $sakitBulanIni = Absensi::whereMonth('tanggal', $currentMonth)
                            ->whereYear('tanggal', $currentYear)
                            ->where('status', 'sakit')
                            ->count();

            $cutiBulanIni = Absensi::whereMonth('tanggal', $currentMonth)
                            ->whereYear('tanggal', $currentYear)
                            ->where('status', 'cuti')
                            ->count();

            // Statistik alpha
            $alphaBulanIni = Absensi::whereMonth('tanggal', $currentMonth)
                            ->whereYear('tanggal', $currentYear)
                            ->where('status', 'alpha')
                            ->count();

            $alphaToday = Absensi::whereDate('tanggal', $today)
                            ->where('status', 'alpha')
                            ->count();

            $alphaYesterday = Absensi::whereDate('tanggal', $yesterday)
                            ->where('status', 'alpha')
                            ->count();

            // Karyawan yang belum absen hari ini
            $usersWithoutAttendanceToday = User::where('role', 'user')
                ->whereNotIn('id', function($query) use ($today) {
                    $query->select('user_id')
                          ->from('absensis')
                          ->whereDate('tanggal', $today);
                })
                ->count();

            // Karyawan dengan alpha terbanyak bulan ini
            $topAlphaUsers = User::select('users.id', 'users.name', 'users.email', DB::raw('COUNT(absensis.id) as alpha_count'))
                ->leftJoin('absensis', function($join) use ($currentMonth, $currentYear) {
                    $join->on('users.id', '=', 'absensis.user_id')
                         ->where('absensis.status', '=', 'alpha')
                         ->whereMonth('absensis.tanggal', $currentMonth)
                         ->whereYear('absensis.tanggal', $currentYear);
                })
                ->where('users.role', 'user')
                ->groupBy('users.id', 'users.name', 'users.email')
                ->having('alpha_count', '>', 0)
                ->orderBy('alpha_count', 'desc')
                ->limit(5)
                ->get();

            // Daftar pegawai yang hadir hari ini
            $pegawaiHadirHariIni = Absensi::with('user')
                                    ->whereDate('tanggal', $today)
                                    ->where('status', 'hadir')
                                    ->orderBy('jam_masuk')
                                    ->limit(5)
                                    ->get();

            // Data untuk kalender
            $calendarData = [];
            $startDate = Carbon::createFromDate($currentYear, $currentMonth, 1)->startOfMonth();
            $endDate = Carbon::createFromDate($currentYear, $currentMonth, 1)->endOfMonth();

            try {
                $absensiCalendar = Absensi::whereBetween('tanggal', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                                    ->get()
                                    ->groupBy(function($item) {
                                        return Carbon::parse($item->tanggal)->format('Y-m-d');
                                    });

                foreach ($absensiCalendar as $date => $absensis) {
                    $hadir = $absensis->where('status', 'hadir')->count();
                    $izin = $absensis->where('status', 'izin')->count();
                    $sakit = $absensis->where('status', 'sakit')->count();
                    $cuti = $absensis->where('status', 'cuti')->count();
                    $alpha = $absensis->where('status', 'alpha')->count();

                    $calendarData[$date] = [
                        'hadir' => $hadir,
                        'izin' => $izin,
                        'sakit' => $sakit,
                        'cuti' => $cuti,
                        'alpha' => $alpha,
                        'total' => $hadir + $izin + $sakit + $cuti + $alpha
                    ];
                }
            } catch (\Exception $e) {
                // Jika terjadi error, tetapkan array kosong
                $calendarData = [];
            }

            // Statistik mingguan untuk grafik
            $weeklyStats = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $hadir = Absensi::whereDate('tanggal', $date->format('Y-m-d'))
                            ->where('status', 'hadir')
                            ->count();
                $izin = Absensi::whereDate('tanggal', $date->format('Y-m-d'))
                            ->whereIn('status', ['izin', 'sakit', 'cuti'])
                            ->count();

                $weeklyStats[] = [
                    'date' => $date->format('d/m'),
                    'day' => $date->format('D'),
                    'hadir' => $hadir,
                    'izin' => $izin
                ];
            }

            // Tidak perlu lagi mengambil pengajuan terbaru untuk ditampilkan di dashboard

            return view('admin.dashboard', compact(
                'userCount',
                'userActive',
                'absensiToday',
                'absensiMonth',
                'izinPending',
                'izinApproved',
                'izinRejected',
                'terlambatBulanIni',
                'tepatWaktuBulanIni',
                'izinBulanIni',
                'sakitBulanIni',
                'cutiBulanIni',
                'alphaBulanIni',
                'alphaToday',
                'alphaYesterday',
                'usersWithoutAttendanceToday',
                'topAlphaUsers',
                'pegawaiHadirHariIni',
                'calendarData',
                'currentMonth',
                'currentYear',
                'weeklyStats'
            ));
        } catch (\Exception $e) {
            // Jika terjadi error, tampilkan dashboard sederhana
            \Log::error('Dashboard Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            $userCount = User::count();
            $userActive = User::where('role', 'user')->count();
            $absensiToday = 0;
            $absensiMonth = 0;
            $izinPending = 0;
            $izinApproved = 0;
            $izinRejected = 0;
            $terlambatBulanIni = 0;
            $tepatWaktuBulanIni = 0;
            $izinBulanIni = 0;
            $sakitBulanIni = 0;
            $cutiBulanIni = 0;
            $alphaBulanIni = 0;
            $alphaToday = 0;
            $alphaYesterday = 0;
            $usersWithoutAttendanceToday = 0;
            $topAlphaUsers = collect();
            $pegawaiHadirHariIni = collect();
            $calendarData = [];
            $currentMonth = date('m');
            $currentYear = date('Y');
            $weeklyStats = [];

            return view('admin.dashboard', compact(
                'userCount',
                'userActive',
                'absensiToday',
                'absensiMonth',
                'izinPending',
                'izinApproved',
                'izinRejected',
                'terlambatBulanIni',
                'tepatWaktuBulanIni',
                'izinBulanIni',
                'sakitBulanIni',
                'cutiBulanIni',
                'alphaBulanIni',
                'alphaToday',
                'alphaYesterday',
                'usersWithoutAttendanceToday',
                'topAlphaUsers',
                'pegawaiHadirHariIni',
                'calendarData',
                'currentMonth',
                'currentYear',
                'weeklyStats'
            ))->with('error', 'Terjadi kesalahan saat memuat data: ' . $e->getMessage());
        }
    }

    public function absensiIndex(Request $request)
    {
        $query = Absensi::with('user');

        // Filter berdasarkan tanggal
        if ($request->filled('tanggal')) {
            $query->whereDate('tanggal', $request->tanggal);
        } else {
            // Default tampilkan data hari ini
            $query->whereDate('tanggal', date('Y-m-d'));
        }

        // Filter berdasarkan status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $absen = $query->orderBy('tanggal', 'desc')
                      ->orderBy('created_at', 'desc')
                      ->paginate(20);

        // Statistik untuk tanggal yang dipilih
        $tanggalFilter = $request->filled('tanggal') ? $request->tanggal : date('Y-m-d');
        $totalPegawai = User::where('role', 'user')->count();

        // Hitung statistik berdasarkan data absensi yang ada untuk tanggal tersebut
        $statsQuery = Absensi::whereDate('tanggal', $tanggalFilter);

        // Apply same filters as main query for consistent stats
        if ($request->filled('status')) {
            $statsQuery->where('status', $request->status);
        }
        if ($request->filled('user_id')) {
            $statsQuery->where('user_id', $request->user_id);
        }

        $totalHadir = (clone $statsQuery)->where('status', 'hadir')->count();
        $totalAlpha = (clone $statsQuery)->where('status', 'alpha')->count();
        $totalIzin = (clone $statsQuery)->whereIn('status', ['izin', 'sakit', 'cuti'])->count();

        // Total absensi yang tercatat untuk tanggal ini
        $totalAbsensiHariIni = $totalHadir + $totalAlpha + $totalIzin;

        // Check if export is requested
        if ($request->has('export') && $request->export === 'pdf') {
            return $this->exportAbsensiPDF($request, $absen, $tanggalFilter);
        }

        return view('admin.absensi.index', compact('absen', 'totalPegawai', 'totalHadir', 'totalAlpha', 'totalIzin', 'totalAbsensiHariIni', 'tanggalFilter'));
    }

    private function exportAbsensiPDF($request, $absen, $tanggalFilter)
    {
        // Get all data without pagination for PDF
        $query = Absensi::with('user');

        // Apply same filters
        if ($request->filled('tanggal')) {
            $query->whereDate('tanggal', $request->tanggal);
        } else {
            $query->whereDate('tanggal', date('Y-m-d'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $allAbsen = $query->orderBy('tanggal', 'desc')
                         ->orderBy('created_at', 'desc')
                         ->get();

        // Statistics
        $totalPegawai = User::where('role', 'user')->count();
        $statsQuery = Absensi::whereDate('tanggal', $tanggalFilter);
        $totalHadir = $statsQuery->where('status', 'hadir')->count();
        $totalAlpha = $statsQuery->where('status', 'alpha')->count();
        $totalIzin = $statsQuery->whereIn('status', ['izin', 'sakit', 'cuti'])->count();

        $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia');

        $pdf = Pdf::loadView('admin.absensi.pdf', compact(
            'allAbsen',
            'tanggalFilter',
            'totalPegawai',
            'totalHadir',
            'totalAlpha',
            'totalIzin',
            'namaPerusahaan'
        ));

        $filename = 'Data_Absensi_' . str_replace('-', '_', $tanggalFilter) . '.pdf';

        return $pdf->download($filename);
    }

    public function rekapAbsensi(Request $request)
    {
        $query = Absensi::with('user')
                ->where('status', 'hadir'); // Hanya tampilkan absensi hadir, bukan izin/cuti/sakit

        // Filter berdasarkan tanggal
        if ($request->filled('tanggal_mulai')) {
            $query->where('tanggal', '>=', $request->tanggal_mulai);
        }

        if ($request->filled('tanggal_akhir')) {
            $query->where('tanggal', '<=', $request->tanggal_akhir);
        }

        // Filter berdasarkan user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $absensis = $query->orderBy('tanggal', 'asc')
                        ->join('users', 'absensis.user_id', '=', 'users.id')
                        ->orderBy('users.name', 'asc')
                        ->select('absensis.*')
                        ->paginate(15);
        $users = User::where('role', 'user')->get();

        return view('admin.absensi.rekap', compact('absensis', 'users'));
    }

    /**
     * Cetak rekap absensi dalam format PDF
     */
    public function cetakPDF(Request $request)
    {
        try {
            $query = Absensi::with('user')
                    ->where('status', 'hadir');

            // Filter berdasarkan tanggal
            if ($request->filled('tanggal_mulai')) {
                $query->where('tanggal', '>=', $request->tanggal_mulai);
            }

            if ($request->filled('tanggal_akhir')) {
                $query->where('tanggal', '<=', $request->tanggal_akhir);
            }

            // Filter berdasarkan user
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            $absensis = $query->orderBy('tanggal', 'asc')
                            ->join('users', 'absensis.user_id', '=', 'users.id')
                            ->orderBy('users.name', 'asc')
                            ->select('absensis.*')
                            ->get();

            // Ambil pengaturan perusahaan
            $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia');
            $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jl. Contoh No. 123, Jakarta');

            // Buat PDF
            $pdf = PDF::loadView('admin.absensi.pdf.rekap', compact('absensis', 'namaPerusahaan', 'alamatPerusahaan'));
            $pdf->setPaper('a4', 'landscape');

            // Download PDF
            return $pdf->download('rekap-absensi-' . date('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal mencetak PDF: ' . $e->getMessage());
        }
    }

    /**
     * Cetak rekap absensi per user dalam format PDF (versi baru)
     */
    public function cetakUserPDF($userId, Request $request)
    {
        try {
            // Cari user
            $user = User::findOrFail($userId);

            // Query untuk absensi hadir
            $query = Absensi::with('user')
                    ->where('user_id', $userId)
                    ->where('status', 'hadir');

            // Filter berdasarkan tanggal
            if ($request->filled('tanggal_mulai')) {
                $query->where('tanggal', '>=', $request->tanggal_mulai);
            }

            if ($request->filled('tanggal_akhir')) {
                $query->where('tanggal', '<=', $request->tanggal_akhir);
            }

            $absensis = $query->orderBy('tanggal', 'asc')->get();

            // Ambil pengaturan perusahaan
            $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia');
            $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jl. Contoh No. 123, Jakarta');

            // Buat PDF
            $pdf = PDF::loadView('admin.absensi.pdf.rekap_user', compact('absensis', 'user', 'namaPerusahaan', 'alamatPerusahaan'));
            $pdf->setPaper('a4', 'portrait');

            // Download PDF
            return $pdf->download('rekap-absensi-' . $user->name . '-' . date('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal mencetak PDF: ' . $e->getMessage());
        }
    }





    /**
     * Menampilkan halaman pengaturan sistem
     */
    public function showSettings()
    {
        $settings = [
            'jam_masuk' => Setting::getValue('jam_masuk', '08:00:00'),
            'jam_pulang' => Setting::getValue('jam_pulang', '17:00:00'),
            'batas_absen_masuk' => Setting::getValue('batas_absen_masuk', '10:00:00'),
            'minimum_jam_kerja' => Setting::getValue('minimum_jam_kerja', '8'),
            'toleransi_keterlambatan' => Setting::getValue('toleransi_keterlambatan', '15'),
            'hari_kerja' => Setting::getValue('hari_kerja', '1,2,3,4,5'),
            'nama_perusahaan' => Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia'),
            'alamat_perusahaan' => Setting::getValue('alamat_perusahaan', 'Jl. Contoh No. 123, Jakarta'),
            'logo_perusahaan' => Setting::getValue('logo_perusahaan', 'logo.png'),
            'warna_tema' => Setting::getValue('warna_tema', '#4e73df'),
        ];

        return view('admin.settings', compact('settings'));
    }

    /**
     * Menyimpan pengaturan sistem
     */
    public function updateSettings(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'jam_masuk' => 'required|string',
                'jam_pulang' => 'required|string',
                'batas_absen_masuk' => 'required|string',
                'minimum_jam_kerja' => 'required|integer|min:1|max:12',
                'toleransi_keterlambatan' => 'required|integer|min:0|max:60',
                'hari_kerja' => 'required|array',
            ]);

            // Simpan pengaturan jam kerja
            Setting::setValue('jam_masuk', $request->jam_masuk . ':00');
            Setting::setValue('jam_pulang', $request->jam_pulang . ':00');
            Setting::setValue('batas_absen_masuk', $request->batas_absen_masuk . ':00');
            Setting::setValue('minimum_jam_kerja', $request->minimum_jam_kerja);
            Setting::setValue('toleransi_keterlambatan', $request->toleransi_keterlambatan);

            // Simpan pengaturan hari kerja
            $hariKerja = implode(',', $request->hari_kerja);
            Setting::setValue('hari_kerja', $hariKerja);

            return redirect('/admin/settings')->with('success', 'Pengaturan berhasil disimpan');
        } catch (\Exception $e) {
            return redirect('/admin/settings')->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Menampilkan rekap izin/cuti/sakit
     */
    public function rekapIzin(Request $request)
    {
        // Filter berdasarkan tanggal
        $tanggalMulai = $request->input('tanggal_mulai', now()->startOfMonth()->format('Y-m-d'));
        $tanggalAkhir = $request->input('tanggal_akhir', now()->endOfMonth()->format('Y-m-d'));

        // Filter berdasarkan user
        $userId = $request->input('user_id');

        // Filter berdasarkan jenis izin
        $jenisIzin = $request->input('jenis');

        // Filter berdasarkan status approval
        $statusApproval = $request->input('status_approval');

        // Query dasar
        $query = Absensi::with('user', 'approver')
            ->whereIn('status', ['izin', 'sakit', 'cuti'])
            ->whereBetween('tanggal', [$tanggalMulai, $tanggalAkhir]);

        // Tambahkan filter user jika ada
        if ($userId) {
            $query->where('user_id', $userId);
        }

        // Tambahkan filter jenis izin jika ada
        if ($jenisIzin) {
            $query->where('status', $jenisIzin);
        }

        // Tambahkan filter status approval jika ada
        if ($statusApproval) {
            $query->where('status_approval', $statusApproval);
        }

        // Ambil semua data pengajuan
        $pengajuanAll = $query->orderBy('tanggal', 'desc')->get();

        // Kelompokkan pengajuan berdasarkan pengajuan_id
        $pengajuanGrup = [];
        $uniquePengajuanIds = [];

        foreach ($pengajuanAll as $pengajuan) {
            if (!empty($pengajuan->pengajuan_id)) {
                if (!in_array($pengajuan->pengajuan_id, $uniquePengajuanIds)) {
                    $uniquePengajuanIds[] = $pengajuan->pengajuan_id;
                    $pengajuanGrup[] = $pengajuan;
                }
            } else {
                // Jika tidak ada pengajuan_id, gunakan ID sebagai pengganti
                if (!in_array('id_' . $pengajuan->id, $uniquePengajuanIds)) {
                    $uniquePengajuanIds[] = 'id_' . $pengajuan->id;
                    $pengajuanGrup[] = $pengajuan;
                }
            }
        }

        // Urutkan pengajuan berdasarkan nama pengguna (abjad)
        usort($pengajuanGrup, function($a, $b) {
            return strcmp($a->user->name, $b->user->name);
        });

        // Ambil daftar user untuk filter
        $users = User::where('role', 'user')->get();

        return view('admin.rekap_izin', compact('pengajuanGrup', 'users', 'tanggalMulai', 'tanggalAkhir', 'userId', 'jenisIzin', 'statusApproval'));
    }

    /**
     * Menampilkan daftar pengajuan izin/cuti/sakit yang perlu disetujui
     */
    public function approvalIndex(Request $request)
    {
        $query = Absensi::with('user')
            ->whereIn('status', ['izin', 'sakit', 'cuti']);

        // Filter berdasarkan status approval
        if ($request->filled('status_approval')) {
            $query->where('status_approval', $request->status_approval);
        }

        // Filter berdasarkan tanggal
        if ($request->filled('tanggal_mulai')) {
            $query->where('tanggal', '>=', $request->tanggal_mulai);
        }

        if ($request->filled('tanggal_akhir')) {
            $query->where('tanggal', '<=', $request->tanggal_akhir);
        }

        // Filter berdasarkan user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter berdasarkan jenis izin
        if ($request->filled('jenis')) {
            $query->where('status', $request->jenis);
        }

        // Ambil semua data pengajuan
        $pengajuanAll = $query->orderBy('tanggal', 'desc')->get();

        // Kelompokkan pengajuan berdasarkan pengajuan_id
        $pengajuanGrup = [];
        $uniquePengajuanIds = [];

        foreach ($pengajuanAll as $pengajuan) {
            if (!empty($pengajuan->pengajuan_id)) {
                if (!in_array($pengajuan->pengajuan_id, $uniquePengajuanIds)) {
                    $uniquePengajuanIds[] = $pengajuan->pengajuan_id;
                    $pengajuanGrup[] = $pengajuan;
                }
            } else {
                // Jika tidak ada pengajuan_id, gunakan ID sebagai pengganti
                if (!in_array('id_' . $pengajuan->id, $uniquePengajuanIds)) {
                    $uniquePengajuanIds[] = 'id_' . $pengajuan->id;
                    $pengajuanGrup[] = $pengajuan;
                }
            }
        }

        // Paginate hasil
        $perPage = 15;
        $page = $request->input('page', 1);
        $offset = ($page - 1) * $perPage;
        $pengajuanPaginated = new \Illuminate\Pagination\LengthAwarePaginator(
            collect($pengajuanGrup)->slice($offset, $perPage),
            count($pengajuanGrup),
            $perPage,
            $page,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        $users = User::where('role', 'user')->get();

        return view('admin.approval.index', compact('pengajuanPaginated', 'users'));
    }

    /**
     * Menampilkan detail pengajuan izin/cuti/sakit
     */
    public function approvalShow($id)
    {
        $pengajuan = Absensi::with(['user', 'approver'])->findOrFail($id);

        // Pastikan pengajuan adalah izin, sakit, atau cuti
        if (!in_array($pengajuan->status, ['izin', 'sakit', 'cuti'])) {
            return redirect()->route('admin.approval.index')->with('error', 'Data bukan merupakan pengajuan izin, sakit, atau cuti');
        }

        return view('admin.approval.show', compact('pengajuan'));
    }

    /**
     * Menyetujui pengajuan izin/cuti/sakit
     */
    public function approvalApprove(Request $request, $id)
    {
        $pengajuan = Absensi::findOrFail($id);

        // Pastikan pengajuan adalah izin, sakit, atau cuti
        if (!in_array($pengajuan->status, ['izin', 'sakit', 'cuti'])) {
            return redirect()->route('admin.approval.index')->with('error', 'Data bukan merupakan pengajuan izin, sakit, atau cuti');
        }

        // Validasi input
        $request->validate([
            'approval_note' => 'nullable|string|max:255',
        ]);

        // Update status approval
        $pengajuan->status_approval = 'approved';
        $pengajuan->approval_note = $request->approval_note;
        $pengajuan->approval_at = now();
        $pengajuan->approved_by = auth()->id();
        $pengajuan->save();

        return redirect()->route('admin.approval.index')->with('success', 'Pengajuan berhasil disetujui');
    }

    /**
     * Menolak pengajuan izin/cuti/sakit
     */
    public function approvalReject(Request $request, $id)
    {
        $pengajuan = Absensi::findOrFail($id);

        // Pastikan pengajuan adalah izin, sakit, atau cuti
        if (!in_array($pengajuan->status, ['izin', 'sakit', 'cuti'])) {
            return redirect()->route('admin.approval.index')->with('error', 'Data bukan merupakan pengajuan izin, sakit, atau cuti');
        }

        // Validasi input
        $request->validate([
            'approval_note' => 'required|string|max:255',
        ]);

        // Update status approval
        $pengajuan->status_approval = 'rejected';
        $pengajuan->approval_note = $request->approval_note;
        $pengajuan->approval_at = now();
        $pengajuan->approved_by = auth()->id();
        $pengajuan->save();

        return redirect()->route('admin.approval.index')->with('success', 'Pengajuan berhasil ditolak');
    }

    /**
     * Cetak rekap pengajuan izin/cuti/sakit dalam format PDF
     */
    public function cetakRekapIzinPDF(Request $request)
    {
        try {
            // Filter berdasarkan tanggal
            $tanggalMulai = $request->input('tanggal_mulai', now()->startOfMonth()->format('Y-m-d'));
            $tanggalAkhir = $request->input('tanggal_akhir', now()->endOfMonth()->format('Y-m-d'));

            // Filter berdasarkan user
            $userId = $request->input('user_id');

            // Filter berdasarkan jenis izin
            $jenisIzin = $request->input('jenis');

            // Filter berdasarkan status approval
            $statusApproval = $request->input('status_approval');

            // Query dasar
            $query = Absensi::with(['user', 'approver'])
                ->whereIn('status', ['izin', 'sakit', 'cuti'])
                ->whereBetween('tanggal', [$tanggalMulai, $tanggalAkhir]);

            // Tambahkan filter user jika ada
            if ($userId) {
                $query->where('user_id', $userId);
            }

            // Tambahkan filter jenis izin jika ada
            if ($jenisIzin) {
                $query->where('status', $jenisIzin);
            }

            // Tambahkan filter status approval jika ada
            if ($statusApproval) {
                $query->where('status_approval', $statusApproval);
            }

            // Ambil semua data pengajuan
            $pengajuanAll = $query->orderBy('created_at', 'desc')->get();

            // Kelompokkan pengajuan berdasarkan pengajuan_id
            $pengajuanGrup = [];
            $uniquePengajuanIds = [];

            foreach ($pengajuanAll as $pengajuan) {
                // Tambahkan properti untuk template PDF
                $pengajuan->tanggal_mulai = $pengajuan->tanggal_awal ?? $pengajuan->tanggal;
                $pengajuan->tanggal_selesai = $pengajuan->tanggal_akhir ?? $pengajuan->tanggal;

                // Hitung jumlah hari
                $startDate = \Carbon\Carbon::parse($pengajuan->tanggal_mulai);
                $endDate = \Carbon\Carbon::parse($pengajuan->tanggal_selesai);
                $pengajuan->jumlah_hari = $startDate->diffInDays($endDate) + 1;

                if (!empty($pengajuan->pengajuan_id)) {
                    if (!in_array($pengajuan->pengajuan_id, $uniquePengajuanIds)) {
                        $uniquePengajuanIds[] = $pengajuan->pengajuan_id;
                        $pengajuanGrup[] = $pengajuan;
                    }
                } else {
                    // Jika tidak ada pengajuan_id, gunakan ID sebagai pengganti
                    if (!in_array('id_' . $pengajuan->id, $uniquePengajuanIds)) {
                        $uniquePengajuanIds[] = 'id_' . $pengajuan->id;
                        $pengajuanGrup[] = $pengajuan;
                    }
                }
            }

            // Urutkan pengajuan berdasarkan nama pengguna (abjad)
            usort($pengajuanGrup, function($a, $b) {
                return strcmp($a->user->name, $b->user->name);
            });

            // Ambil pengaturan perusahaan
            $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia');
            $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jl. Contoh No. 123, Jakarta');

            // Buat PDF
            $pdf = PDF::loadView('admin.absensi.pdf.rekap_izin', compact('pengajuanGrup', 'namaPerusahaan', 'alamatPerusahaan', 'tanggalMulai', 'tanggalAkhir'));
            $pdf->setPaper('a4', 'landscape');

            // Download PDF
            return $pdf->download('rekap-pengajuan-' . date('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            // Log error untuk debugging
            \Log::error('Error cetak PDF: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return redirect()->back()->with('error', 'Gagal mencetak PDF: ' . $e->getMessage());
        }
    }

    /**
     * Absen manual oleh admin
     */
    public function absenManual(Request $request)
    {
        try {
            // Debug: Log input data
            \Log::info('Absen Manual Input:', $request->all());
            $request->validate([
                'user_id' => 'required|exists:users,id',
                'tanggal' => 'required|date',
                'status' => 'required|in:hadir,izin,sakit,cuti,alpha',
                'jam_masuk' => 'nullable|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
                'jam_keluar' => 'nullable|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
                'keterangan' => 'nullable|string|max:255',
            ], [
                'user_id.required' => 'Karyawan harus dipilih.',
                'user_id.exists' => 'Karyawan tidak valid.',
                'tanggal.required' => 'Tanggal harus diisi.',
                'tanggal.date' => 'Format tanggal tidak valid.',
                'status.required' => 'Status absensi harus dipilih.',
                'status.in' => 'Status absensi tidak valid.',
                'jam_masuk.regex' => 'Format jam masuk harus HH:MM (contoh: 08:30).',
                'jam_keluar.regex' => 'Format jam keluar harus HH:MM (contoh: 17:30).',
                'keterangan.max' => 'Keterangan maksimal 255 karakter.',
            ]);

            // Validasi tambahan untuk status hadir
            if ($request->status === 'hadir') {
                if (empty($request->jam_masuk) || empty($request->jam_keluar)) {
                    return redirect()->back()
                        ->withErrors(['jam_masuk' => 'Jam masuk dan jam keluar harus diisi untuk status hadir.'])
                        ->withInput();
                }

                // Validasi jam keluar harus setelah jam masuk
                $jamMasuk = \Carbon\Carbon::createFromFormat('H:i', $request->jam_masuk);
                $jamKeluar = \Carbon\Carbon::createFromFormat('H:i', $request->jam_keluar);

                if ($jamKeluar <= $jamMasuk) {
                    return redirect()->back()
                        ->withErrors(['jam_keluar' => 'Jam keluar harus lebih besar dari jam masuk.'])
                        ->withInput();
                }
            }

            // Cek apakah sudah ada absensi untuk user dan tanggal tersebut
            $existingAbsensi = Absensi::where('user_id', $request->user_id)
                ->whereDate('tanggal', $request->tanggal)
                ->first();

            if ($existingAbsensi) {
                return redirect()->back()->with('error', 'Absensi untuk karyawan ini pada tanggal tersebut sudah ada.');
            }

            // Buat data absensi
            $data = [
                'user_id' => $request->user_id,
                'tanggal' => $request->tanggal,
                'status' => $request->status,
                'keterangan' => $request->keterangan ?? 'Absen manual oleh admin',
            ];

            // Jika status hadir, tambahkan jam masuk dan keluar
            if ($request->status === 'hadir') {
                if ($request->jam_masuk) {
                    $data['jam_masuk'] = $request->tanggal . ' ' . $request->jam_masuk . ':00';
                }
                if ($request->jam_keluar) {
                    $data['jam_keluar'] = $request->tanggal . ' ' . $request->jam_keluar . ':00';
                }
            }

            // Simpan absensi
            Absensi::create($data);

            $user = User::find($request->user_id);
            $statusText = ucfirst($request->status);

            return redirect()->back()->with('success', "Absensi manual berhasil disimpan untuk {$user->name} dengan status {$statusText}.");

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error absen manual: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Tampilkan daftar pengajuan absen manual
     */
    public function manualAttendanceRequests(Request $request)
    {
        $query = \App\Models\ManualAttendanceRequest::with(['user', 'processedBy']);

        // Filter berdasarkan status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan tanggal
        if ($request->filled('tanggal')) {
            $query->whereDate('tanggal', $request->tanggal);
        }

        // Filter berdasarkan user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::where('role', 'user')->get();

        // Statistik
        $totalPending = \App\Models\ManualAttendanceRequest::where('status', 'pending')->count();
        $totalApproved = \App\Models\ManualAttendanceRequest::where('status', 'approved')->count();
        $totalRejected = \App\Models\ManualAttendanceRequest::where('status', 'rejected')->count();

        return view('admin.manual_attendance_requests.index', compact(
            'requests',
            'users',
            'totalPending',
            'totalApproved',
            'totalRejected'
        ));
    }

    /**
     * Tampilkan detail pengajuan absen manual
     */
    public function showManualAttendanceRequest($id)
    {
        $request = \App\Models\ManualAttendanceRequest::with(['user', 'processedBy'])->findOrFail($id);

        return view('admin.manual_attendance_requests.show', compact('request'));
    }

    /**
     * Setujui pengajuan absen manual
     */
    public function approveManualAttendanceRequest(Request $request, $id)
    {
        try {
            $manualRequest = \App\Models\ManualAttendanceRequest::findOrFail($id);

            if ($manualRequest->status !== 'pending') {
                return redirect()->back()->with('error', 'Pengajuan sudah diproses sebelumnya.');
            }

            // Cek apakah sudah ada absensi untuk tanggal tersebut
            $existingAbsensi = Absensi::where('user_id', $manualRequest->user_id)
                ->whereDate('tanggal', $manualRequest->tanggal)
                ->first();

            if ($existingAbsensi) {
                return redirect()->back()->with('error', 'User sudah memiliki absensi untuk tanggal tersebut.');
            }

            // Buat record absensi berdasarkan pengajuan
            $absensiData = [
                'user_id' => $manualRequest->user_id,
                'tanggal' => $manualRequest->tanggal,
                'status' => 'hadir',
                'keterangan' => 'Absen manual - ' . $manualRequest->alasan,
                'catatan' => 'Disetujui oleh admin: ' . auth()->user()->name,
                'status_approval' => 'approved',
            ];

            // Set jam berdasarkan jenis absen
            switch ($manualRequest->jenis_absen) {
                case 'masuk':
                    $absensiData['jam_masuk'] = $manualRequest->jam_masuk;
                    break;
                case 'keluar':
                    $absensiData['jam_keluar'] = $manualRequest->jam_keluar;
                    break;
                case 'masuk_keluar':
                    $absensiData['jam_masuk'] = $manualRequest->jam_masuk;
                    $absensiData['jam_keluar'] = $manualRequest->jam_keluar;
                    break;
            }

            Absensi::create($absensiData);

            // Update status pengajuan
            $manualRequest->update([
                'status' => 'approved',
                'admin_notes' => $request->input('admin_notes'),
                'processed_by' => auth()->id(),
                'processed_at' => now(),
            ]);

            return redirect()->back()->with('success', 'Pengajuan absen manual berhasil disetujui dan absensi telah dibuat.');

        } catch (\Exception $e) {
            \Log::error('Error approving manual attendance request: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Tolak pengajuan absen manual
     */
    public function rejectManualAttendanceRequest(Request $request, $id)
    {
        try {
            $request->validate([
                'admin_notes' => 'required|string|max:500',
            ], [
                'admin_notes.required' => 'Alasan penolakan harus diisi.',
                'admin_notes.max' => 'Alasan penolakan maksimal 500 karakter.',
            ]);

            $manualRequest = \App\Models\ManualAttendanceRequest::findOrFail($id);

            if ($manualRequest->status !== 'pending') {
                return redirect()->back()->with('error', 'Pengajuan sudah diproses sebelumnya.');
            }

            // Update status pengajuan
            $manualRequest->update([
                'status' => 'rejected',
                'admin_notes' => $request->input('admin_notes'),
                'processed_by' => auth()->id(),
                'processed_at' => now(),
            ]);

            return redirect()->back()->with('success', 'Pengajuan absen manual berhasil ditolak.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error rejecting manual attendance request: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }


}
