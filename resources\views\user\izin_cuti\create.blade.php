@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Form Pengajuan Izin/Cuti/Sakit</h5>
                    <!-- Tombol kembali mengarah ke halaman dasbor pengguna -->
                    <a href="{{ route('dashboard') }}" class="btn btn-sm btn-light">
                        <i class="bi bi-arrow-left"></i> Kembali Ke Dashboard
                    </a>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                    
                    <form method="POST" action="{{ route('izin-cuti.store') }}" enctype="multipart/form-data" id="izinCutiForm">
                        @csrf
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="jenis" class="form-label">Jenis Pengajuan <span class="text-danger">*</span></label>
                                    <select name="jenis" id="jenis" class="form-select @error('jenis') is-invalid @enderror" required>
                                        <option value="">-- Pilih Jenis --</option>
                                        <option value="izin" {{ old('jenis') == 'izin' ? 'selected' : '' }}>Izin</option>
                                        <option value="sakit" {{ old('jenis') == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                        <option value="cuti" {{ old('jenis') == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                    </select>
                                    @error('jenis')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dokumen" class="form-label">Dokumen Pendukung</label>
                                    <input type="file" name="dokumen" id="dokumen" class="form-control @error('dokumen') is-invalid @enderror">
                                    <small class="text-muted">Upload surat dokter (untuk sakit) atau dokumen pendukung lainnya (opsional)</small>
                                    @error('dokumen')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tanggal_mulai" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                    <input type="date" name="tanggal_mulai" id="tanggal_mulai" class="form-control @error('tanggal_mulai') is-invalid @enderror" value="{{ old('tanggal_mulai') }}" required>
                                    @error('tanggal_mulai')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tanggal_selesai" class="form-label">Tanggal Selesai <span class="text-danger">*</span></label>
                                    <input type="date" name="tanggal_selesai" id="tanggal_selesai" class="form-control @error('tanggal_selesai') is-invalid @enderror" value="{{ old('tanggal_selesai') }}" required>
                                    @error('tanggal_selesai')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan <span class="text-danger">*</span></label>
                            <textarea name="keterangan" id="keterangan" rows="3" class="form-control @error('keterangan') is-invalid @enderror" required>{{ old('keterangan') }}</textarea>
                            @error('keterangan')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label for="signature-pad" class="form-label">Tanda Tangan Digital <span class="text-danger">*</span></label>
                            <div class="border rounded p-3 bg-white">
                                <canvas id="signature-pad" class="signature-pad" width="100%" height="200"></canvas>
                            </div>
                            <div class="d-flex justify-content-end mt-2">
                                <button type="button" id="clear-signature" class="btn btn-sm btn-secondary">
                                    <i class="bi bi-eraser"></i> Hapus
                                </button>
                            </div>
                            <input type="hidden" name="tanda_tangan" id="tanda_tangan">
                            @error('tanda_tangan')
                                <div class="text-danger small">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send"></i> Kirim Pengajuan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tanda tangan digital
    const signaturePad = new SignaturePad(document.getElementById('signature-pad'), {
        backgroundColor: 'rgba(255, 255, 255, 0)',
        penColor: 'black',
        minWidth: 1,
        maxWidth: 3
    });
    
    document.getElementById('clear-signature').addEventListener('click', function() {
        signaturePad.clear();
    });
    
    // Form submit
    document.getElementById('izinCutiForm').addEventListener('submit', function(e) {
        if (signaturePad.isEmpty()) {
            e.preventDefault();
            alert('Silakan isi tanda tangan Anda');
            return false;
        }
        
        // Simpan tanda tangan sebagai base64
        document.getElementById('tanda_tangan').value = signaturePad.toDataURL();
    });
    
    // Validasi tanggal
    document.getElementById('tanggal_selesai').addEventListener('change', function() {
        const tanggalMulai = new Date(document.getElementById('tanggal_mulai').value);
        const tanggalSelesai = new Date(this.value);
        
        if (tanggalSelesai < tanggalMulai) {
            alert('Tanggal selesai tidak boleh lebih awal dari tanggal mulai');
            this.value = document.getElementById('tanggal_mulai').value;
        }
    });
    
    document.getElementById('tanggal_mulai').addEventListener('change', function() {
        const tanggalMulai = new Date(this.value);
        const tanggalSelesai = new Date(document.getElementById('tanggal_selesai').value);
        
        if (tanggalSelesai < tanggalMulai) {
            document.getElementById('tanggal_selesai').value = this.value;
        }
    });
});
</script>

<style>
.signature-pad {
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    height: 200px;
    background-color: white;
}
</style>
@endsection
