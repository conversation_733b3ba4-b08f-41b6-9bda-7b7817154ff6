<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SalaryDeduction;
use App\Models\Absensi;

class CleanupIncorrectDeductions extends Command
{
    protected $signature = 'deductions:cleanup-incorrect';
    protected $description = 'Cleanup salary deductions that were incorrectly created for izin/cuti/sakit';

    public function handle()
    {
        $this->info('Starting cleanup of incorrect salary deductions...');
        
        // Find deductions that are linked to izin/cuti/sakit attendance records
        $incorrectDeductions = SalaryDeduction::whereHas('absensi', function($query) {
            $query->whereIn('status', ['izin', 'cuti', 'sakit']);
        })->get();
        
        if ($incorrectDeductions->count() > 0) {
            $this->warn("Found {$incorrectDeductions->count()} incorrect deductions for izin/cuti/sakit:");
            
            foreach ($incorrectDeductions as $deduction) {
                $this->line("- ID: {$deduction->id}, User: {$deduction->user->name}, Status: {$deduction->absensi->status}, Amount: Rp " . number_format($deduction->jumlah_potongan));
            }
            
            if ($this->confirm('Do you want to delete these incorrect deductions?')) {
                $deletedCount = 0;
                foreach ($incorrectDeductions as $deduction) {
                    $this->line("Deleting deduction ID {$deduction->id} for {$deduction->user->name} ({$deduction->absensi->status})");
                    $deduction->delete();
                    $deletedCount++;
                }
                
                $this->info("Successfully deleted {$deletedCount} incorrect deductions.");
            } else {
                $this->info('Cleanup cancelled.');
            }
        } else {
            $this->info('No incorrect deductions found. System is clean!');
        }
        
        // Show summary of current deductions
        $this->info("\nCurrent deduction summary:");
        $alphaDeductions = SalaryDeduction::where('jenis', 'alpha')->count();
        $lateDeductions = SalaryDeduction::where('jenis', 'terlambat')->count();
        $spDeductions = SalaryDeduction::whereIn('jenis', ['sp1', 'sp2', 'sp3'])->count();
        $otherDeductions = SalaryDeduction::whereNotIn('jenis', ['alpha', 'terlambat', 'sp1', 'sp2', 'sp3'])->count();
        
        $this->table(['Type', 'Count'], [
            ['Alpha', $alphaDeductions],
            ['Terlambat', $lateDeductions],
            ['SP (1/2/3)', $spDeductions],
            ['Lainnya', $otherDeductions],
        ]);
        
        $this->info('Cleanup completed!');
    }
}
