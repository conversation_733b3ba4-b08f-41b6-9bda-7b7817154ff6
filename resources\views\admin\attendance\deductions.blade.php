@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<style>
    .card-header {
        background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e40af 100%) !important;
        border: none;
        position: relative;
        overflow: hidden;
        min-height: 120px;
    }

    .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(29, 78, 216, 0.15) 0%, transparent 50%);
        opacity: 1;
    }

    .header-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(37, 99, 235, 0.3) 100%);
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        backdrop-filter: blur(20px);
        border: 2px solid rgba(59, 130, 246, 0.6);
        box-shadow:
            0 8px 32px rgba(15, 23, 42, 0.3),
            inset 0 1px 0 rgba(59, 130, 246, 0.4);
    }

    .btn-header-action {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(37, 99, 235, 0.3) 100%);
        border: 2px solid rgba(59, 130, 246, 0.6);
        color: white;
        padding: 0.875rem 1.75rem;
        border-radius: 16px;
        font-weight: 700;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        box-shadow:
            0 6px 25px rgba(15, 23, 42, 0.2),
            inset 0 1px 0 rgba(59, 130, 246, 0.4);
        position: relative;
        overflow: hidden;
        text-shadow: 0 1px 2px rgba(15, 23, 42, 0.3);
    }

    .btn-header-action:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.5) 100%);
        transform: translateY(-3px);
        box-shadow:
            0 15px 45px rgba(15, 23, 42, 0.3),
            inset 0 1px 0 rgba(59, 130, 246, 0.6);
        color: white;
        border-color: rgba(59, 130, 246, 0.8);
    }

    .btn-header-secondary {
        background: transparent;
        border: 2px solid rgba(59, 130, 246, 0.7);
        color: white;
        padding: 0.875rem 1.75rem;
        border-radius: 16px;
        font-weight: 700;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
        text-shadow: 0 1px 2px rgba(15, 23, 42, 0.3);
    }

    .btn-header-secondary:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(37, 99, 235, 0.2) 100%);
        border-color: rgba(59, 130, 246, 1);
        transform: translateY(-3px);
        color: white;
        box-shadow: 0 10px 35px rgba(15, 23, 42, 0.2);
    }

    .filter-card {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border: 1px solid #4b5563;
        border-radius: 1rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        color: white;
    }

    .filter-card .form-select, .filter-card .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        color: white;
        backdrop-filter: blur(10px);
    }

    .filter-card .form-select:focus, .filter-card .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        background: rgba(255, 255, 255, 0.15);
    }

    .filter-card .form-select option {
        background: #374151;
        color: white;
    }

    .btn-filter {
        background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
        border: none;
        color: white;
        padding: 0.5rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(30, 58, 138, 0.4);
        background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
        color: white;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header text-white py-4 position-relative">
                    <div class="header-bg"></div>
                    <div class="container-fluid position-relative">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="header-icon me-4">
                                        <i class="bi bi-dash-circle"></i>
                                    </div>
                                    <div>
                                        <h3 class="mb-1 fw-bold text-white">Kelola Potongan Gaji</h3>
                                        <div class="d-flex align-items-center text-white-50">
                                            <i class="bi bi-calendar3 me-2"></i>
                                            <span class="fw-medium">Periode: {{ \DateTime::createFromFormat('!m', $bulan)->format('F') }} {{ $tahun }}</span>
                                        </div>
                                        <div class="d-flex align-items-center text-white-50 mt-1">
                                            <i class="bi bi-clock me-2"></i>
                                            <span class="small">Terakhir diperbarui: {{ date('H:i') }} WIB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex gap-2 justify-content-end flex-wrap">
                                    <a href="{{ route('admin.attendance.cuti-stats') }}" class="btn btn-header-action">
                                        <i class="bi bi-calendar-check me-2"></i>
                                        <span>Monitor Cuti</span>
                                    </a>
                                    <button type="button" class="btn btn-header-action" data-bs-toggle="modal" data-bs-target="#createDeductionModal">
                                        <i class="bi bi-plus me-2"></i>
                                        <span>Buat Potongan</span>
                                    </button>
                                    <a href="{{ route('admin.attendance.index') }}" class="btn btn-header-secondary">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        <span>Kembali</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <!-- Auto-Clean Notification -->
                    @if(session('info'))
                        <div class="alert alert-info border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%); color: white;">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check-circle-fill" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">🔄 Auto-Sync Berhasil</h6>
                                    <p class="mb-0">{{ session('info') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Filter Section -->
                    <div class="filter-card p-4 mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-10">
                                <form method="GET" action="{{ route('admin.attendance.deductions') }}" class="d-flex gap-3 flex-wrap align-items-center">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="bi bi-funnel text-white-50"></i>
                                        <span class="text-white-50 fw-medium">Filter:</span>
                                    </div>
                                    <select name="bulan" class="form-select" style="min-width: 140px;">
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ $bulan == $i ? 'selected' : '' }}>
                                                {{ \DateTime::createFromFormat('!m', $i)->format('F') }}
                                            </option>
                                        @endfor
                                    </select>
                                    <select name="tahun" class="form-select" style="min-width: 100px;">
                                        @for($i = date('Y') - 1; $i <= date('Y'); $i++)
                                            <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                                        @endfor
                                    </select>
                                    <select name="user_id" class="form-select" style="min-width: 180px;">
                                        <option value="">Semua Karyawan</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <select name="jenis" class="form-select" style="min-width: 140px;">
                                        <option value="">Semua Jenis</option>
                                        <option value="alpha" {{ $jenis == 'alpha' ? 'selected' : '' }}>Alpha</option>
                                        <option value="terlambat" {{ $jenis == 'terlambat' ? 'selected' : '' }}>Terlambat</option>
                                        <option value="sp1" {{ $jenis == 'sp1' ? 'selected' : '' }}>SP 1</option>
                                        <option value="sp2" {{ $jenis == 'sp2' ? 'selected' : '' }}>SP 2</option>
                                        <option value="sp3" {{ $jenis == 'sp3' ? 'selected' : '' }}>SP 3</option>
                                        <option value="lainnya" {{ $jenis == 'lainnya' ? 'selected' : '' }}>Lainnya</option>
                                    </select>
                                    <button type="submit" class="btn btn-filter">
                                        <i class="bi bi-search me-2"></i>
                                        Filter
                                    </button>
                                    <a href="{{ route('admin.attendance.deductions') }}" class="btn btn-outline-light">
                                        <i class="bi bi-arrow-clockwise me-2"></i>
                                        Reset
                                    </a>
                                </form>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="d-flex gap-2 justify-content-end flex-wrap">
                                    <button class="btn btn-outline-warning btn-sm" onclick="cleanOrphanedDeductions()" title="Bersihkan potongan yang tidak sinkron">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="cleanInvalidDeductions()" title="Bersihkan potongan untuk izin/cuti/sakit">
                                        <i class="bi bi-shield-x"></i>
                                    </button>
                                    <button class="btn btn-outline-light" onclick="exportToPDF()">
                                        <i class="bi bi-file-earmark-pdf me-2"></i>
                                        Cetak PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div class="alert alert-primary border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%); color: white;">
                        <div class="d-flex align-items-center">
                            <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-2 fw-bold">📋 Aturan Potongan Gaji</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="mb-0 small">
                                            <li><strong class="text-danger">Alpha:</strong> Rp {{ number_format(\App\Models\Setting::getValue('potongan_alpha', 100000)) }} per hari</li>
                                            <li><strong class="text-warning">Terlambat:</strong> Rp {{ number_format(\App\Models\Setting::getValue('potongan_terlambat', 50000)) }} per hari</li>
                                            <li><strong class="text-secondary">SP 1/2/3:</strong> Sesuai kebijakan perusahaan</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="mb-0 small">
                                            <li><strong class="text-success">✅ Izin:</strong> TIDAK dipotong gaji</li>
                                            <li><strong class="text-success">✅ Sakit:</strong> TIDAK dipotong gaji</li>
                                            <li><strong class="text-success">✅ Cuti:</strong> TIDAK dipotong gaji (max 12 hari/tahun)</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="mt-2 p-2 rounded" style="background: rgba(255,255,255,0.1);">
                                    <small><i class="bi bi-shield-check me-1"></i><strong>Catatan:</strong> Sistem otomatis hanya memotong gaji untuk Alpha dan Terlambat. Izin, Sakit, dan Cuti tidak akan dipotong gaji sesuai kebijakan perusahaan.</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistik Potongan -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);">
                                <div class="card-body text-white text-center p-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="card-title mb-0 fw-bold">Total Potongan</h6>
                                        <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-dash-circle-fill fs-4"></i>
                                        </div>
                                    </div>
                                    <h3 class="mb-2 fw-bold">Rp {{ number_format($stats['total_amount']) }}</h3>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <i class="bi bi-list-ul me-2"></i>
                                        <small class="opacity-75">{{ $stats['total_count'] }} item</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);">
                                <div class="card-body text-white text-center p-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="card-title mb-0 fw-bold">Alpha</h6>
                                        <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-x-circle-fill fs-4"></i>
                                        </div>
                                    </div>
                                    <h3 class="mb-2 fw-bold">{{ $stats['alpha_count'] }}</h3>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <i class="bi bi-currency-dollar me-2"></i>
                                        <small class="opacity-75">Rp {{ number_format($stats['alpha_amount']) }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%);">
                                <div class="card-body text-white text-center p-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="card-title mb-0 fw-bold">Terlambat</h6>
                                        <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-clock-fill fs-4"></i>
                                        </div>
                                    </div>
                                    <h3 class="mb-2 fw-bold">{{ $stats['terlambat_count'] }}</h3>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <i class="bi bi-currency-dollar me-2"></i>
                                        <small class="opacity-75">Rp {{ number_format($stats['terlambat_amount']) }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-lg h-100" style="background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);">
                                <div class="card-body text-white text-center p-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="card-title mb-0 fw-bold">Surat Peringatan</h6>
                                        <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-exclamation-triangle-fill fs-4"></i>
                                        </div>
                                    </div>
                                    <h3 class="mb-2 fw-bold">{{ $stats['sp_count'] }}</h3>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <i class="bi bi-currency-dollar me-2"></i>
                                        <small class="opacity-75">Rp {{ number_format($stats['sp_amount']) }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabel Potongan -->
                    <div class="table-responsive rounded-3 overflow-hidden shadow-sm">
                        <table class="table table-hover align-middle mb-0">
                            <thead style="background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e40af 100%); color: white;">
                                <tr>
                                    <th class="ps-4 border-0 fw-bold">No</th>
                                    <th class="border-0 fw-bold">Karyawan</th>
                                    <th class="border-0 fw-bold">Tanggal</th>
                                    <th class="border-0 fw-bold">Jenis</th>
                                    <th class="border-0 fw-bold">Jumlah Potongan</th>
                                    <th class="border-0 fw-bold">Keterangan</th>
                                    <th class="border-0 fw-bold">Status</th>
                                    <th class="pe-4 border-0 fw-bold">Aksi</th>
                                </tr>
                            </thead>
                            <tbody style="background: white;">
                                @forelse($deductions as $index => $deduction)
                                    <tr style="border-bottom: 1px solid #e5e7eb;">
                                        <td class="ps-4 fw-medium">{{ $deductions->firstItem() + $index }}</td>
                                        <td>
                                            <div>
                                                <div class="fw-bold text-dark">{{ $deduction->user->name }}</div>
                                                <small class="text-muted">{{ $deduction->user->jabatan ?? 'Karyawan' }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-calendar3 me-2 text-muted"></i>
                                                <span class="fw-medium">{{ \Carbon\Carbon::parse($deduction->tanggal)->format('d/m/Y') }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $jenisColors = [
                                                    'alpha' => 'danger',
                                                    'terlambat' => 'warning',
                                                    'sp1' => 'info',
                                                    'sp2' => 'primary',
                                                    'sp3' => 'dark',
                                                    'lainnya' => 'secondary'
                                                ];
                                                $color = $jenisColors[$deduction->jenis] ?? 'secondary';
                                                $jenisLabels = [
                                                    'alpha' => 'Alpha',
                                                    'terlambat' => 'Terlambat',
                                                    'sp1' => 'SP 1',
                                                    'sp2' => 'SP 2',
                                                    'sp3' => 'SP 3',
                                                    'lainnya' => 'Lainnya'
                                                ];
                                                $label = $jenisLabels[$deduction->jenis] ?? ucfirst($deduction->jenis);
                                            @endphp
                                            <span class="badge bg-{{ $color }} px-3 py-2 fw-medium">{{ $label }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-currency-dollar me-2 text-danger"></i>
                                                <span class="fw-bold text-danger fs-6">
                                                    Rp {{ number_format($deduction->jumlah_potongan) }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            @if($deduction->keterangan)
                                                <div class="text-truncate" style="max-width: 200px;" title="{{ $deduction->keterangan }}">
                                                    <small class="text-muted">{{ $deduction->keterangan }}</small>
                                                </div>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($deduction->status == 'approved')
                                                <span class="badge bg-success px-3 py-2 fw-medium">
                                                    <i class="bi bi-check-circle me-1"></i>Disetujui
                                                </span>
                                            @elseif($deduction->status == 'pending')
                                                <span class="badge bg-warning px-3 py-2 fw-medium">
                                                    <i class="bi bi-clock me-1"></i>Pending
                                                </span>
                                            @else
                                                <span class="badge bg-danger px-3 py-2 fw-medium">
                                                    <i class="bi bi-x-circle me-1"></i>Ditolak
                                                </span>
                                            @endif
                                        </td>
                                        <td class="pe-4">
                                            <div class="btn-group" role="group">
                                                <!-- Lihat Detail -->
                                                <button type="button" class="btn btn-sm btn-outline-info rounded-pill me-1"
                                                        onclick="viewDeduction({{ $deduction->id }})" title="Lihat Detail">
                                                    <i class="bi bi-eye"></i>
                                                </button>

                                                <!-- Edit (hanya untuk status pending atau manual) -->
                                                @if($deduction->status == 'pending' || !$deduction->absensi_id)
                                                    <button type="button" class="btn btn-sm btn-outline-warning rounded-pill me-1"
                                                            onclick="editDeduction({{ $deduction->id }})" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                @endif

                                                <!-- Approve/Reject (hanya untuk status pending) -->
                                                @if($deduction->status == 'pending')
                                                    <button type="button" class="btn btn-sm btn-outline-success rounded-pill me-1"
                                                            onclick="approveDeduction({{ $deduction->id }})" title="Setujui">
                                                        <i class="bi bi-check-circle"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger rounded-pill me-1"
                                                            onclick="rejectDeduction({{ $deduction->id }})" title="Tolak">
                                                        <i class="bi bi-x-circle"></i>
                                                    </button>
                                                @endif

                                                <!-- Lihat Absensi Terkait -->
                                                @if($deduction->absensi)
                                                    <a href="{{ route('admin.approval.show', $deduction->absensi->id) }}"
                                                       class="btn btn-sm btn-outline-primary rounded-pill me-1" title="Lihat Absensi">
                                                        <i class="bi bi-calendar-check"></i>
                                                    </a>
                                                @endif

                                                <!-- Hapus (hanya untuk manual atau pending) -->
                                                @if($deduction->status == 'pending' || !$deduction->absensi_id)
                                                    <button type="button" class="btn btn-sm btn-outline-danger rounded-pill"
                                                            onclick="deleteDeduction({{ $deduction->id }})" title="Hapus">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-5">
                                            <div class="text-muted">
                                                <div class="mb-3">
                                                    <i class="bi bi-inbox display-1 text-muted opacity-50"></i>
                                                </div>
                                                <h5 class="text-muted">Tidak ada data potongan</h5>
                                                <p class="mb-0">Belum ada potongan gaji untuk periode ini</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($deductions->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            <div class="pagination-wrapper">
                                {{ $deductions->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Buat Potongan Manual -->
<div class="modal fade" id="createDeductionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <form method="POST" action="{{ route('admin.attendance.deductions.create') }}">
                @csrf
                <div class="modal-header" style="background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%); color: white;">
                    <div class="d-flex align-items-center">
                        <div class="me-3" style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-plus-circle"></i>
                        </div>
                        <h5 class="modal-title mb-0 fw-bold">Buat Potongan Manual</h5>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-person me-2 text-primary"></i>Karyawan
                                </label>
                                <select name="user_id" class="form-select" required>
                                    <option value="">Pilih Karyawan</option>
                                    @foreach(\App\Models\User::where('role', 'user')->get() as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }} - {{ $user->jabatan ?? 'Karyawan' }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-calendar3 me-2 text-primary"></i>Tanggal
                                </label>
                                <input type="date" name="tanggal" class="form-control" value="{{ date('Y-m-d') }}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-tag me-2 text-primary"></i>Jenis Potongan
                                </label>
                                <select name="jenis" class="form-select" required>
                                    <option value="alpha">Alpha</option>
                                    <option value="terlambat">Terlambat</option>
                                    <option value="sp1">SP 1</option>
                                    <option value="sp2">SP 2</option>
                                    <option value="sp3">SP 3</option>
                                    <option value="lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-currency-dollar me-2 text-primary"></i>Jumlah Potongan (Rp)
                                </label>
                                <input type="number" name="jumlah_potongan" class="form-control" min="0" step="1000" required placeholder="0">
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="bi bi-chat-text me-2 text-primary"></i>Keterangan
                                </label>
                                <textarea name="keterangan" class="form-control" rows="4" required
                                          placeholder="Alasan potongan gaji..."></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning border-0 mt-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill me-3"></i>
                            <div>
                                <strong>Perhatian:</strong> Potongan manual akan langsung disetujui dan mempengaruhi gaji karyawan.
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-plus-circle me-2"></i>Buat Potongan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal View Detail -->
<div class="modal fade" id="viewDeductionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header" style="background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%); color: white;">
                <div class="d-flex align-items-center">
                    <div class="me-3" style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="bi bi-eye"></i>
                    </div>
                    <h5 class="modal-title mb-0 fw-bold">Detail Potongan Gaji</h5>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4" id="viewDeductionContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Potongan -->
<div class="modal fade" id="editDeductionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <form id="editDeductionForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-header" style="background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%); color: white;">
                    <div class="d-flex align-items-center">
                        <div class="me-3" style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-pencil"></i>
                        </div>
                        <h5 class="modal-title mb-0 fw-bold">Edit Potongan Gaji</h5>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4" id="editDeductionContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function deleteDeduction(id) {
    if (confirm('Hapus potongan gaji ini?\n\nTindakan ini tidak dapat dibatalkan.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/attendance/deductions/${id}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.location.href = '{{ route("admin.attendance.deductions") }}?' + params.toString();
}

function viewDeduction(id) {
    fetch(`/admin/attendance/deductions/${id}/view`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('viewDeductionContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('viewDeductionModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Gagal memuat detail potongan');
        });
}

function editDeduction(id) {
    fetch(`/admin/attendance/deductions/${id}/edit`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('editDeductionContent').innerHTML = html;
            document.getElementById('editDeductionForm').action = `/admin/attendance/deductions/${id}`;
            new bootstrap.Modal(document.getElementById('editDeductionModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Gagal memuat form edit');
        });
}

function approveDeduction(id) {
    if (confirm('Setujui potongan gaji ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/attendance/deductions/${id}/approve`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PATCH';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

function rejectDeduction(id) {
    const reason = prompt('Alasan penolakan:');
    if (reason !== null && reason.trim() !== '') {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/attendance/deductions/${id}/reject`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PATCH';
        form.appendChild(methodInput);

        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'reason';
        reasonInput.value = reason;
        form.appendChild(reasonInput);

        document.body.appendChild(form);
        form.submit();
    }
}

function cleanOrphanedDeductions() {
    if (confirm('🔄 BERSIHKAN POTONGAN TIDAK SINKRON\n\nSistem akan:\n✓ Menghapus potongan alpha yang absensinya sudah berubah ke hadir/izin/sakit/cuti\n✓ Menghapus potongan terlambat yang absensinya sudah berubah status\n✓ Menjaga konsistensi data\n\nLanjutkan?')) {
        const params = new URLSearchParams(window.location.search);
        window.location.href = '{{ route("admin.attendance.deductions.clean") }}?' + params.toString();
    }
}

function cleanInvalidDeductions() {
    if (confirm('🛡️ BERSIHKAN POTONGAN TIDAK VALID\n\nSistem akan:\n✓ Menghapus potongan gaji untuk karyawan yang sedang izin/cuti/sakit yang disetujui\n✓ Memastikan izin, cuti, dan sakit TIDAK dipotong gaji\n✓ Sesuai kebijakan perusahaan\n\nLanjutkan pembersihan?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.attendance.deductions.clean-invalid") }}';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endsection
