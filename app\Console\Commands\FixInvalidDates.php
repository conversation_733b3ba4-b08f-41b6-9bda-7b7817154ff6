<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Absensi;
use Carbon\Carbon;

class FixInvalidDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:invalid-dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix invalid date formats that cause trailing data errors';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing invalid dates...');

        $this->fixUserDates();
        $this->fixAbsensiDates();

        $this->info('✅ All invalid dates have been fixed!');
        return 0;
    }

    private function fixUserDates()
    {
        $this->info('Checking User dates...');
        
        $users = User::all();
        $fixedCount = 0;

        foreach ($users as $user) {
            $updated = false;

            // Fix tanggal_lahir
            if ($user->tanggal_lahir && !$this->isValidDate($user->tanggal_lahir)) {
                $user->tanggal_lahir = null;
                $updated = true;
                $this->line("  - Fixed tanggal_lahir for user: {$user->name}");
            }

            // Fix tanggal_bergabung
            if ($user->tanggal_bergabung && !$this->isValidDate($user->tanggal_bergabung)) {
                $user->tanggal_bergabung = null;
                $updated = true;
                $this->line("  - Fixed tanggal_bergabung for user: {$user->name}");
            }

            // Fix tanggal_sp_terakhir
            if ($user->tanggal_sp_terakhir && !$this->isValidDate($user->tanggal_sp_terakhir)) {
                $user->tanggal_sp_terakhir = null;
                $updated = true;
                $this->line("  - Fixed tanggal_sp_terakhir for user: {$user->name}");
            }

            if ($updated) {
                $user->save();
                $fixedCount++;
            }
        }

        $this->info("✓ Fixed {$fixedCount} user records");
    }

    private function fixAbsensiDates()
    {
        $this->info('Checking Absensi dates...');
        
        $absensis = Absensi::all();
        $fixedCount = 0;

        foreach ($absensis as $absensi) {
            $updated = false;

            // Fix tanggal
            if ($absensi->tanggal && !$this->isValidDate($absensi->tanggal)) {
                // Try to extract valid date from invalid format
                $fixedDate = $this->extractValidDate($absensi->tanggal);
                if ($fixedDate) {
                    $absensi->tanggal = $fixedDate;
                    $updated = true;
                    $this->line("  - Fixed tanggal for absensi ID: {$absensi->id}");
                }
            }

            // Fix tanggal_mulai
            if ($absensi->tanggal_mulai && !$this->isValidDate($absensi->tanggal_mulai)) {
                $fixedDate = $this->extractValidDate($absensi->tanggal_mulai);
                if ($fixedDate) {
                    $absensi->tanggal_mulai = $fixedDate;
                    $updated = true;
                    $this->line("  - Fixed tanggal_mulai for absensi ID: {$absensi->id}");
                }
            }

            // Fix tanggal_selesai
            if ($absensi->tanggal_selesai && !$this->isValidDate($absensi->tanggal_selesai)) {
                $fixedDate = $this->extractValidDate($absensi->tanggal_selesai);
                if ($fixedDate) {
                    $absensi->tanggal_selesai = $fixedDate;
                    $updated = true;
                    $this->line("  - Fixed tanggal_selesai for absensi ID: {$absensi->id}");
                }
            }

            if ($updated) {
                $absensi->save();
                $fixedCount++;
            }
        }

        $this->info("✓ Fixed {$fixedCount} absensi records");
    }

    private function isValidDate($date)
    {
        try {
            Carbon::parse($date);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function extractValidDate($invalidDate)
    {
        try {
            // Try to extract YYYY-MM-DD pattern
            if (preg_match('/(\d{4}-\d{2}-\d{2})/', $invalidDate, $matches)) {
                $date = Carbon::parse($matches[1]);
                return $date->format('Y-m-d');
            }

            // Try to extract DD/MM/YYYY pattern
            if (preg_match('/(\d{2}\/\d{2}\/\d{4})/', $invalidDate, $matches)) {
                $date = Carbon::createFromFormat('d/m/Y', $matches[1]);
                return $date->format('Y-m-d');
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }
}
