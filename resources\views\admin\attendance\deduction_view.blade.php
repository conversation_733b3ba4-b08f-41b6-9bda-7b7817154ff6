<div class="row">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="bi bi-person me-2"></i>Informasi <PERSON></h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5 class="mb-1">{{ $deduction->user->name }}</h5>
                    <p class="text-muted mb-0">{{ $deduction->user->jabatan ?? 'Karyawan' }}</p>
                    <small class="text-muted">NIK: {{ $deduction->user->nik ?? '-' }}</small>
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Detail <PERSON></h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Tanggal:</strong>
                        <p class="mb-2">{{ \Carbon\Carbon::parse($deduction->tanggal)->format('d/m/Y') }}</p>
                    </div>
                    <div class="col-6">
                        <strong>Periode:</strong>
                        <p class="mb-2">{{ \DateTime::createFromFormat('!m', $deduction->bulan)->format('F') }} {{ $deduction->tahun }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-6">
                        <strong>Jenis:</strong>
                        @php
                            $jenisColors = [
                                'alpha' => 'danger',
                                'terlambat' => 'warning',
                                'sp1' => 'info',
                                'sp2' => 'primary',
                                'sp3' => 'dark',
                                'lainnya' => 'secondary'
                            ];
                            $color = $jenisColors[$deduction->jenis] ?? 'secondary';
                            $jenisLabels = [
                                'alpha' => 'Alpha',
                                'terlambat' => 'Terlambat',
                                'sp1' => 'SP 1',
                                'sp2' => 'SP 2',
                                'sp3' => 'SP 3',
                                'lainnya' => 'Lainnya'
                            ];
                            $label = $jenisLabels[$deduction->jenis] ?? ucfirst($deduction->jenis);
                        @endphp
                        <p class="mb-2"><span class="badge bg-{{ $color }}">{{ $label }}</span></p>
                    </div>
                    <div class="col-6">
                        <strong>Jumlah Potongan:</strong>
                        <p class="mb-2 text-danger fw-bold">Rp {{ number_format($deduction->jumlah_potongan) }}</p>
                    </div>
                </div>

                <div class="mb-3">
                    <strong>Keterangan:</strong>
                    <p class="mb-0">{{ $deduction->keterangan }}</p>
                </div>

                @if($deduction->catatan)
                    <div class="mb-3">
                        <strong>Catatan Admin:</strong>
                        <p class="mb-0 text-muted">{{ $deduction->catatan }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="bi bi-check-circle me-2"></i>Status & Approval</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Status:</strong>
                    @if($deduction->status == 'approved')
                        <span class="badge bg-success ms-2">
                            <i class="bi bi-check-circle me-1"></i>Disetujui
                        </span>
                    @elseif($deduction->status == 'pending')
                        <span class="badge bg-warning ms-2">
                            <i class="bi bi-clock me-1"></i>Pending
                        </span>
                    @else
                        <span class="badge bg-danger ms-2">
                            <i class="bi bi-x-circle me-1"></i>Ditolak
                        </span>
                    @endif
                </div>

                @if($deduction->creator)
                    <div class="mb-3">
                        <strong>Dibuat oleh:</strong>
                        <p class="mb-0">{{ $deduction->creator->name }}</p>
                        <small class="text-muted">{{ $deduction->created_at->format('d/m/Y H:i') }}</small>
                    </div>
                @endif

                @if($deduction->approver && $deduction->tanggal_disetujui)
                    <div class="mb-3">
                        <strong>{{ $deduction->status == 'approved' ? 'Disetujui' : 'Ditolak' }} oleh:</strong>
                        <p class="mb-0">{{ $deduction->approver->name }}</p>
                        <small class="text-muted">{{ $deduction->tanggal_disetujui->format('d/m/Y H:i') }}</small>
                    </div>
                @endif
            </div>
        </div>

        @if($deduction->absensi)
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0"><i class="bi bi-calendar-check me-2"></i>Data Absensi Terkait</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Tanggal:</strong>
                            <p class="mb-2">{{ \Carbon\Carbon::parse($deduction->absensi->tanggal)->format('d/m/Y') }}</p>
                        </div>
                        <div class="col-6">
                            <strong>Status:</strong>
                            <p class="mb-2">
                                @if($deduction->absensi->status == 'hadir')
                                    <span class="badge bg-success">Hadir</span>
                                @elseif($deduction->absensi->status == 'terlambat')
                                    <span class="badge bg-warning">Terlambat</span>
                                @elseif($deduction->absensi->status == 'alpha')
                                    <span class="badge bg-danger">Alpha</span>
                                @else
                                    <span class="badge bg-secondary">{{ ucfirst($deduction->absensi->status) }}</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    @if($deduction->absensi->jam_masuk)
                        <div class="row">
                            <div class="col-6">
                                <strong>Jam Masuk:</strong>
                                <p class="mb-2">{{ \Carbon\Carbon::parse($deduction->absensi->jam_masuk)->format('H:i') }}</p>
                            </div>
                            @if($deduction->absensi->jam_keluar)
                                <div class="col-6">
                                    <strong>Jam Keluar:</strong>
                                    <p class="mb-2">{{ \Carbon\Carbon::parse($deduction->absensi->jam_keluar)->format('H:i') }}</p>
                                </div>
                            @endif
                        </div>
                    @endif

                    <div class="mt-3">
                        <a href="{{ route('admin.approval.show', $deduction->absensi->id) }}"
                           class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye me-1"></i>Lihat Detail Absensi
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

@if($deduction->status == 'pending')
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="bi bi-gear me-2"></i>Aksi</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" onclick="approveDeduction({{ $deduction->id }})">
                            <i class="bi bi-check-circle me-2"></i>Setujui
                        </button>
                        <button type="button" class="btn btn-danger" onclick="rejectDeduction({{ $deduction->id }})">
                            <i class="bi bi-x-circle me-2"></i>Tolak
                        </button>
                        @if(!$deduction->absensi_id)
                            <button type="button" class="btn btn-warning" onclick="editDeduction({{ $deduction->id }})">
                                <i class="bi bi-pencil me-2"></i>Edit
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
