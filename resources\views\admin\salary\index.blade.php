@extends('layouts.admin')

@section('title', 'Manajemen Gaji')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-cash-stack me-2"></i>
                            Manajemen Gaji Bulanan
                        </h5>
                        <button type="button" class="btn btn-light btn-sm" onclick="openGenerateModal()">
                            <i class="bi bi-plus-circle me-1"></i>
                            Generate Gaji
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Info Panel -->
                    @if($salaries->isEmpty())
                        <div class="alert alert-info border-0 mb-4">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-info-circle-fill me-3 fs-4"></i>
                                <div>
                                    <h6 class="mb-1">Belum Ada Gaji untuk Periode Ini</h6>
                                    <p class="mb-2">Klik tombol <strong>"Generate Gaji"</strong> di atas untuk membuat slip gaji otomatis berdasarkan:</p>
                                    <ul class="mb-0">
                                        <li>Data kehadiran karyawan</li>
                                        <li>Jam lembur yang sudah disetujui</li>
                                        <li>Potongan gaji (alpha, terlambat, SP)</li>
                                        <li>Gaji pokok dan tunjangan dari profil karyawan</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="GET" action="{{ route('admin.salary.index') }}" class="d-flex gap-2">
                                <select name="bulan" class="form-select">
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ $bulan == $i ? 'selected' : '' }}>
                                            {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                        </option>
                                    @endfor
                                </select>
                                <select name="tahun" class="form-select">
                                    @for($i = date('Y') - 2; $i <= date('Y') + 1; $i++)
                                        <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                        @if(!$salaries->isEmpty())
                            <div class="col-md-6 text-end">
                                <button type="button" class="btn btn-success btn-sm" onclick="generateCurrentMonth()">
                                    <i class="bi bi-lightning-fill me-1"></i>
                                    Generate Bulan Ini
                                </button>
                            </div>
                        @endif
                    </div>

                    <!-- Tabel Gaji -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Karyawan</th>
                                    <th>Periode</th>
                                    <th>Gaji Pokok</th>
                                    <th>Total Lembur</th>
                                    <th>Total Potongan</th>
                                    <th>Gaji Bersih</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($salaries as $index => $salary)
                                    <tr>
                                        <td>{{ $salaries->firstItem() + $index }}</td>
                                        <td>
                                            <div>
                                                <div class="fw-bold">{{ $salary->user->name }}</div>
                                                <small class="text-muted">{{ $salary->user->jabatan }}</small>
                                            </div>
                                        </td>
                                        <td>{{ $salary->periode }}</td>
                                        <td>Rp {{ number_format($salary->gaji_pokok, 0, ',', '.') }}</td>
                                        <td>
                                            <div>{{ $salary->total_jam_lembur }} jam</div>
                                            <small class="text-success">Rp {{ number_format($salary->total_upah_lembur, 0, ',', '.') }}</small>
                                        </td>
                                        <td>
                                            <span class="text-danger">Rp {{ number_format($salary->total_potongan, 0, ',', '.') }}</span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-primary">Rp {{ number_format($salary->gaji_bersih, 0, ',', '.') }}</span>
                                        </td>
                                        <td>
                                            <span class="badge {{ $salary->status_badge }}">{{ $salary->status_text }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.salary.show', $salary->id) }}"
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                @if($salary->status == 'draft')
                                                    <form method="POST" action="{{ route('admin.salary.approve', $salary->id) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-success"
                                                                onclick="return confirm('Setujui slip gaji ini?')">
                                                            <i class="bi bi-check-circle"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                                @if($salary->status == 'approved')
                                                    <form method="POST" action="{{ route('admin.salary.mark-paid', $salary->id) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-primary"
                                                                onclick="return confirm('Tandai sebagai dibayar?')">
                                                            <i class="bi bi-cash"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                                <a href="{{ route('admin.salary.print', $salary->id) }}"
                                                   class="btn btn-sm btn-outline-success"
                                                   title="Cetak PDF">
                                                    <i class="bi bi-printer"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4"></i>
                                                <p class="mt-2">Belum ada data gaji untuk periode ini</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($salaries->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $salaries->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Generate Gaji -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.salary.generate') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Generate Gaji Bulanan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Bulan</label>
                            <select name="bulan" class="form-select" required>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ date('n') == $i ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Tahun</label>
                            <select name="tahun" class="form-select" required>
                                @for($i = date('Y') - 1; $i <= date('Y'); $i++)
                                    <option value="{{ $i }}" {{ date('Y') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                @endfor
                            </select>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Karyawan</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAll" checked>
                            <label class="form-check-label fw-bold" for="selectAll">
                                Pilih Semua Karyawan
                            </label>
                        </div>
                        <div class="mt-2" style="max-height: 200px; overflow-y: auto;">
                            @foreach(\App\Models\User::where('role', 'user')->get() as $user)
                                <div class="form-check">
                                    <input class="form-check-input user-checkbox" type="checkbox"
                                           name="user_ids[]" value="{{ $user->id }}" id="user{{ $user->id }}" checked>
                                    <label class="form-check-label" for="user{{ $user->id }}">
                                        {{ $user->name }} - {{ $user->jabatan }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Generate Gaji</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing salary page...');

    // Event listener untuk select all checkbox
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
});

function openGenerateModal() {
    console.log('Opening generate modal...');
    try {
        const modal = new bootstrap.Modal(document.getElementById('generateModal'));
        modal.show();
    } catch (error) {
        console.error('Error opening modal:', error);
        alert('Error membuka modal. Silakan refresh halaman.');
    }
}

function generateCurrentMonth() {
    if (confirm('🚀 GENERATE GAJI BULAN INI\n\nSistem akan otomatis:\n✓ Generate gaji untuk semua karyawan\n✓ Hitung kehadiran dan lembur\n✓ Terapkan potongan otomatis\n✓ Buat slip gaji siap approve\n\nLanjutkan proses?')) {
        try {
            // Set current month and year in modal
            const currentMonth = new Date().getMonth() + 1;
            const currentYear = new Date().getFullYear();

            console.log('Current month:', currentMonth, 'Current year:', currentYear);

            // Open modal and set values
            const modal = new bootstrap.Modal(document.getElementById('generateModal'));

            // Set values in modal
            const bulanSelect = document.querySelector('#generateModal select[name="bulan"]');
            const tahunSelect = document.querySelector('#generateModal select[name="tahun"]');

            if (bulanSelect) {
                bulanSelect.value = currentMonth;
                console.log('Set month to:', currentMonth);
            }
            if (tahunSelect) {
                tahunSelect.value = currentYear;
                console.log('Set year to:', currentYear);
            }

            // Check all users
            const selectAll = document.getElementById('selectAll');
            if (selectAll) {
                selectAll.checked = true;
                console.log('Checked select all');
            }

            document.querySelectorAll('.user-checkbox').forEach(checkbox => {
                checkbox.checked = true;
            });

            modal.show();
        } catch (error) {
            console.error('Error in generateCurrentMonth:', error);
            alert('Error: ' + error.message);
        }
    }
}
</script>
@endsection
