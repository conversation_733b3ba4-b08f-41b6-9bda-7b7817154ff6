# 📋 Aturan Potongan Gaji Sistem Absensi

## ✅ **SISTEM SUDAH BENAR - KONFIRMASI**

Berdasarkan analisis kode dan database, sistem potongan gaji sudah berfungsi dengan benar sesuai kebijakan perusahaan.

## 🔍 **Aturan Potongan Gaji**

### **Yang DIPOTONG Gaji:**
1. **🔴 Alpha (Tanpa Keterangan)**
   - Potongan: Rp 100,000 per hari
   - Otomatis dibuat oleh sistem jika karyawan tidak absen
   - Memicu surat peringatan jika berulang

2. **🟡 Terlambat**
   - Potongan: Rp 50,000 per hari
   - Berlaku jika terlambat lebih dari toleransi (15 menit)
   - Hanya untuk status "hadir" yang terlambat

3. **⚫ Surat Peringatan (SP)**
   - SP1: Rp 100,000 (setelah 3x alpha/terlambat)
   - SP2: Rp 100,000 (setelah 5x alpha/terlambat)
   - SP3: Rp 100,000 (setelah 7x alpha/terlambat)

### **Yang TIDAK DIPOTONG Gaji:**
1. **✅ Izin** - Tidak ada potongan gaji
2. **✅ Sakit** - Tidak ada potongan gaji
3. **✅ Cuti** - Tidak ada potongan gaji (maksimal 12 hari/tahun)

## 🛠️ **Implementasi Teknis**

### **Command yang Memproses Potongan:**
1. `attendance:check-daily` - Membuat alpha dan potongannya
2. `attendance:process-lateness` - Memproses keterlambatan

### **File Terkait:**
- `app/Console/Commands/CheckDailyAttendance.php`
- `app/Console/Commands/ProcessLatenessDeductions.php`
- `app/Models/SalaryDeduction.php`
- `app/Http/Controllers/Admin/AttendanceManagementController.php`

### **Database:**
- Tabel: `salary_deductions`
- Jenis yang valid: `alpha`, `terlambat`, `sp1`, `sp2`, `sp3`, `lainnya`

## 📊 **Status Database Saat Ini:**
```
+------------+-------+
| Type       | Count |
+------------+-------+
| Alpha      | 110   |
| Terlambat  | 5     |
| SP (1/2/3) | 15    |
| Lainnya    | 0     |
+------------+-------+
```

## 🔧 **Command Maintenance:**
```bash
# Membersihkan potongan yang salah (jika ada)
php artisan deductions:cleanup-incorrect

# Proses harian untuk tanggal tertentu
php artisan attendance:check-daily 2024-01-15
php artisan attendance:process-lateness 2024-01-15
```

## 🎯 **Kesimpulan:**
✅ Sistem sudah benar - Izin, Cuti, dan Sakit TIDAK dipotong gaji
✅ Hanya Alpha dan Terlambat yang dipotong gaji
✅ Database bersih dari potongan yang salah
✅ UI sudah menampilkan aturan dengan jelas
