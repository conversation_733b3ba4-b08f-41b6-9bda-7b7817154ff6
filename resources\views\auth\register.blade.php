@extends('layouts.app')

@section('content')
<div class="register-fullwidth">
    <div class="register-bg"></div>
    <div class="register-overlay"></div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-9">
                <div class="row g-0">
                    <!-- Left Side - Register Form -->
                    <div class="col-md-8">
                        <div class="register-card">
                            <div class="text-center mb-3">
                                <div class="app-logo mb-2 register-logo">
                                    <i class="bi bi-calendar-check"></i>
                                </div>
                                <h3 class="fw-bold">Buat Akun Baru</h3>
                                <p class="text-muted small">Lengkapi data diri Anda untuk mendaftar</p>
                            </div>

                            @if(session('error'))
                                <div class="alert alert-danger alert-dismissible fade show py-2">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    {{ session('error') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            @if($errors->any())
                                <div class="alert alert-danger alert-dismissible fade show py-2">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <ul class="mb-0 ps-3 small">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('register') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Lengkap</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                                        <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" placeholder="Masukkan nama lengkap Anda" value="{{ old('name') }}" required autofocus>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                        <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror" placeholder="Masukkan email Anda" value="{{ old('email') }}" required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                        <input type="password" name="password" id="password" class="form-control @error('password') is-invalid @enderror" placeholder="Masukkan password Anda" required>
                                        <button type="button" class="input-group-text toggle-password" tabindex="-1">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Konfirmasi Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                        <input type="password" name="password_confirmation" id="password_confirmation" class="form-control" placeholder="Konfirmasi password Anda" required>
                                        <button type="button" class="input-group-text toggle-confirm-password" tabindex="-1">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-success register-btn">
                                        <i class="bi bi-person-plus me-2"></i> Register
                                    </button>
                                </div>

                                <div class="text-center mb-3">
                                    <p class="mb-2 small">Sudah punya akun? <a href="{{ route('login') }}" class="fw-bold">Login sekarang!</a></p>
                                    <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-arrow-left me-1"></i> Kembali ke halaman utama
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Right Side - Features -->
                    <div class="col-md-4 d-none d-md-block">
                        <div class="register-features-sidebar">
                            <div class="features-header">
                                <h4>Keunggulan Aplikasi</h4>
                                <p class="text-muted small">Nikmati berbagai fitur unggulan kami</p>
                            </div>

                            <div class="feature-item-sidebar">
                                <div class="feature-icon register-feature-icon">
                                    <i class="bi bi-shield-check"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Keamanan Terjamin</h5>
                                    <p class="small">Data Anda selalu aman dan terenkripsi</p>
                                </div>
                            </div>

                            <div class="feature-item-sidebar">
                                <div class="feature-icon register-feature-icon">
                                    <i class="bi bi-lightning-charge"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Proses Cepat</h5>
                                    <p class="small">Absensi hanya dalam hitungan detik</p>
                                </div>
                            </div>

                            <div class="feature-item-sidebar">
                                <div class="feature-icon register-feature-icon">
                                    <i class="bi bi-person-badge"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Profil Lengkap</h5>
                                    <p class="small">Kelola informasi profil dengan mudah</p>
                                </div>
                            </div>

                            <div class="feature-item-sidebar">
                                <div class="feature-icon register-feature-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Laporan Detail</h5>
                                    <p class="small">Lihat statistik kehadiran secara real-time</p>
                                </div>
                            </div>

                            <div class="feature-item-sidebar">
                                <div class="feature-icon register-feature-icon">
                                    <i class="bi bi-camera"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Foto & Tanda Tangan</h5>
                                    <p class="small">Absensi dengan foto dan tanda tangan digital</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Reset navbar styles for register page */
.navbar {
    display: none !important;
}

body, html {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    width: 100%;
    height: 100%;
}

.container {
    position: relative;
    z-index: 10;
}

/* Full width background */
.register-fullwidth {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    min-height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.register-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: blur(0px);
    z-index: 1;
}

.register-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(28, 200, 138, 0.9) 0%, rgba(25, 135, 84, 0.8) 100%);
    z-index: 2;
}

/* Register Card */
.register-card {
    background: white;
    border-radius: 10px 0 0 10px;
    padding: 25px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    position: relative;
    z-index: 10;
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    height: 100%;
}

.register-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.18);
}

/* App Logo */
.app-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.75rem;
    color: white;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.app-logo::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

.register-logo {
    background: linear-gradient(135deg, #1cc88a 0%, #169a6f 100%);
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
}

.form-control {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 10px 12px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #1cc88a;
    box-shadow: 0 0 0 0.2rem rgba(28, 200, 138, 0.15);
}

.input-group-text {
    background-color: #f8f9fc;
    border: 1px solid #e2e8f0;
    color: #6c757d;
    font-size: 0.9rem;
}

.input-group .form-control {
    border-radius: 0 6px 6px 0;
}

.input-group .input-group-text:first-child {
    border-radius: 6px 0 0 6px;
}

.toggle-password, .toggle-confirm-password {
    cursor: pointer;
    border-radius: 0 6px 6px 0;
}

.toggle-password:hover, .toggle-confirm-password:hover {
    background-color: #e9ecef;
}

/* Button Styles */
.register-btn {
    background: linear-gradient(135deg, #1cc88a 0%, #169a6f 100%);
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.register-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
    z-index: -1;
}

.register-btn:hover::before {
    left: 100%;
}

.register-btn:hover {
    background: linear-gradient(135deg, #169a6f 0%, #0f7757 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(28, 200, 138, 0.25);
}

/* Features Sidebar */
.register-features-sidebar {
    height: 100%;
    padding: 25px 20px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 0 10px 10px 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-left: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.features-header {
    text-align: center;
    margin-bottom: 20px;
    color: white;
}

.features-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.features-header p {
    opacity: 0.9;
}

.feature-item-sidebar {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.2);
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.feature-item-sidebar:hover {
    transform: translateX(5px);
    background: rgba(255, 255, 255, 0.3);
}

.feature-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.register-feature-icon {
    background: linear-gradient(135deg, #1cc88a 0%, #169a6f 100%);
}

.feature-content {
    color: white;
}

.feature-content h5 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.feature-content p {
    margin-bottom: 0;
    opacity: 0.9;
    line-height: 1.3;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    .register-card {
        padding: 25px 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .register-fullwidth {
        padding: 20px 0;
    }

    /* Add mobile features */
    .register-card form::after {
        content: '';
        display: block;
        margin-top: 20px;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }

    .register-card form::after {
        content: 'Keunggulan Aplikasi: Keamanan Terjamin, Proses Cepat, Profil Lengkap';
        display: block;
        text-align: center;
        font-size: 0.8rem;
        color: #666;
        margin-top: 20px;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.querySelector('.toggle-password');
    const passwordInput = document.querySelector('#password');

    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle icon
            const icon = this.querySelector('i');
            icon.classList.toggle('bi-eye');
            icon.classList.toggle('bi-eye-slash');
        });
    }

    // Toggle confirm password visibility
    const toggleConfirmPassword = document.querySelector('.toggle-confirm-password');
    const confirmPasswordInput = document.querySelector('#password_confirmation');

    if (toggleConfirmPassword && confirmPasswordInput) {
        toggleConfirmPassword.addEventListener('click', function() {
            const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            confirmPasswordInput.setAttribute('type', type);

            // Toggle icon
            const icon = this.querySelector('i');
            icon.classList.toggle('bi-eye');
            icon.classList.toggle('bi-eye-slash');
        });
    }
});
</script>
@endsection
