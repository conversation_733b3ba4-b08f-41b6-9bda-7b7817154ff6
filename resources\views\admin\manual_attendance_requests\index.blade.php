@extends('layouts.admin')

@section('title', 'Pengajuan A<PERSON>en Manual')

@section('content')
<style>
    .card-header {
        background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e40af 100%) !important;
        border: none;
        position: relative;
        overflow: hidden;
        min-height: 120px;
    }

    .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(29, 78, 216, 0.15) 0%, transparent 50%);
        opacity: 1;
    }

    .header-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(37, 99, 235, 0.3) 100%);
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        backdrop-filter: blur(20px);
        border: 2px solid rgba(59, 130, 246, 0.6);
        box-shadow:
            0 8px 32px rgba(15, 23, 42, 0.3),
            inset 0 1px 0 rgba(59, 130, 246, 0.4);
    }

    .stats-card {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border: 1px solid #4b5563;
        border-radius: 1rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        color: white;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .filter-card {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border: 1px solid #4b5563;
        border-radius: 1rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        color: white;
    }

    .filter-card .form-select, .filter-card .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        color: white;
        backdrop-filter: blur(10px);
    }

    .filter-card .form-select:focus, .filter-card .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        background: rgba(255, 255, 255, 0.15);
    }

    .filter-card .form-select option {
        background: #374151;
        color: white;
    }

    .btn-filter {
        background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
        border: none;
        color: white;
        padding: 0.5rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(30, 58, 138, 0.4);
        background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
        color: white;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(59, 130, 246, 0.05) !important;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-group .btn {
        transition: all 0.3s ease;
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
        z-index: 2;
    }

    .modal-content {
        border-radius: 1rem;
        border: none;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }

    .modal-header {
        border-radius: 1rem 1rem 0 0;
        border-bottom: none;
    }

    .modal-footer {
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 1rem 1rem;
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.05);
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .stats-card:hover h3 {
        animation: pulse 1s ease-in-out;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header text-white py-4 position-relative">
                    <div class="header-bg"></div>
                    <div class="container-fluid position-relative">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="header-icon me-4">
                                        <i class="bi bi-person-raised-hand"></i>
                                    </div>
                                    <div>
                                        <h3 class="mb-1 fw-bold text-white">Pengajuan Absen Manual</h3>
                                        <div class="d-flex align-items-center text-white-50">
                                            <i class="bi bi-calendar3 me-2"></i>
                                            <span class="fw-medium">Kelola pengajuan absen manual dari karyawan</span>
                                        </div>
                                        <div class="d-flex align-items-center text-white-50 mt-1">
                                            <i class="bi bi-clock me-2"></i>
                                            <span class="small">Terakhir diperbarui: {{ date('H:i') }} WIB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex gap-2 justify-content-end">
                                    <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-light">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        <span>Kembali</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">

                    <!-- Alert Messages -->
                    @if(session('success'))
                        <div class="alert alert-success border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #10b981 0%, #34d399 100%); color: white;">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check-circle-fill" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">✅ Berhasil</h6>
                                    <p class="mb-0">{{ session('success') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #ef4444 0%, #f87171 100%); color: white;">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-exclamation-triangle-fill" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">❌ Error</h6>
                                    <p class="mb-0">{{ session('error') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-4 mb-3">
                            <div class="stats-card h-100 p-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="text-warning text-uppercase mb-1 fw-bold" style="font-size: 0.75rem; letter-spacing: 1px;">Menunggu Persetujuan</h6>
                                        <h3 class="mb-0 fw-bold">{{ $totalPending }}</h3>
                                    </div>
                                    <div class="stats-icon" style="background: linear-gradient(135deg, rgba(251, 191, 36, 0.3) 0%, rgba(245, 158, 11, 0.2) 100%);">
                                        <i class="bi bi-clock-history text-warning"></i>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center text-white-50">
                                    <i class="bi bi-hourglass-split me-2"></i>
                                    <small>Perlu ditinjau</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="stats-card h-100 p-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="text-success text-uppercase mb-1 fw-bold" style="font-size: 0.75rem; letter-spacing: 1px;">Disetujui</h6>
                                        <h3 class="mb-0 fw-bold">{{ $totalApproved }}</h3>
                                    </div>
                                    <div class="stats-icon" style="background: linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%);">
                                        <i class="bi bi-check-circle text-success"></i>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center text-white-50">
                                    <i class="bi bi-check-all me-2"></i>
                                    <small>Sudah diproses</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="stats-card h-100 p-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="text-danger text-uppercase mb-1 fw-bold" style="font-size: 0.75rem; letter-spacing: 1px;">Ditolak</h6>
                                        <h3 class="mb-0 fw-bold">{{ $totalRejected }}</h3>
                                    </div>
                                    <div class="stats-icon" style="background: linear-gradient(135deg, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 38, 0.2) 100%);">
                                        <i class="bi bi-x-circle text-danger"></i>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center text-white-50">
                                    <i class="bi bi-x-octagon me-2"></i>
                                    <small>Tidak disetujui</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Form -->
                    <div class="filter-card p-4 mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-10">
                                <form method="GET" action="{{ route('admin.manual.attendance.requests') }}" class="d-flex gap-3 flex-wrap align-items-center">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="bi bi-funnel text-white-50"></i>
                                        <span class="text-white-50 fw-medium">Filter:</span>
                                    </div>
                                    <select name="status" class="form-select" style="min-width: 140px;">
                                        <option value="">Semua Status</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Disetujui</option>
                                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Ditolak</option>
                                    </select>
                                    <input type="date" name="tanggal" class="form-control" style="min-width: 160px;" value="{{ request('tanggal') }}" placeholder="Tanggal Absen">
                                    <select name="user_id" class="form-select" style="min-width: 180px;">
                                        <option value="">Semua Karyawan</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <button type="submit" class="btn btn-filter">
                                        <i class="bi bi-search me-2"></i>
                                        Filter
                                    </button>
                                    <a href="{{ route('admin.manual.attendance.requests') }}" class="btn btn-outline-light">
                                        <i class="bi bi-arrow-clockwise me-2"></i>
                                        Reset
                                    </a>
                                </form>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="d-flex gap-2 justify-content-end">
                                    <span class="badge bg-info fs-6">{{ $requests->total() }} Total</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Info Panel -->
                    <div class="alert border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%); color: white;">
                        <div class="d-flex align-items-center">
                            <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-2 fw-bold">📋 Tentang Pengajuan Absen Manual</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="mb-0 small">
                                            <li><strong>Tujuan:</strong> Untuk karyawan yang tidak dapat melakukan absen otomatis</li>
                                            <li><strong>Proses:</strong> Pengajuan → Review Admin → Approve/Reject</li>
                                            <li><strong>Otomatis:</strong> Jika disetujui, absensi akan dibuat otomatis</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="mb-0 small">
                                            <li><strong>Validasi:</strong> Sistem cek duplikasi dan konflik jadwal</li>
                                            <li><strong>Notifikasi:</strong> Karyawan mendapat update status real-time</li>
                                            <li><strong>Audit:</strong> Semua pengajuan tercatat dengan timestamp</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    @if($requests->count() > 0)
                        <div class="table-responsive rounded-3 overflow-hidden shadow-sm">
                            <table class="table table-hover align-middle mb-0">
                                <thead style="background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e40af 100%); color: white;">
                                    <tr>
                                        <th class="ps-4 border-0 fw-bold">No</th>
                                        <th class="border-0 fw-bold">Karyawan</th>
                                        <th class="border-0 fw-bold">Tanggal</th>
                                        <th class="border-0 fw-bold">Jenis Absen</th>
                                        <th class="border-0 fw-bold">Jam</th>
                                        <th class="border-0 fw-bold">Status</th>
                                        <th class="border-0 fw-bold">Pengajuan</th>
                                        <th class="pe-4 border-0 fw-bold">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody style="background: white;">
                                    @foreach($requests as $index => $request)
                                        <tr style="border-bottom: 1px solid #e5e7eb;">
                                            <td class="ps-4 fw-medium">{{ $requests->firstItem() + $index }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; font-size: 14px; font-weight: bold;">
                                                        {{ strtoupper(substr($request->user->name, 0, 2)) }}
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold text-dark">{{ $request->user->name }}</div>
                                                        <small class="text-muted">{{ $request->user->jabatan ?? 'Karyawan' }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-calendar3 me-2 text-muted"></i>
                                                    <div>
                                                        <div class="fw-medium">{{ $request->tanggal->format('d/m/Y') }}</div>
                                                        <small class="text-muted">{{ $request->tanggal->format('l') }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @php
                                                    $jenisColors = [
                                                        'masuk' => 'success',
                                                        'keluar' => 'warning',
                                                        'masuk_keluar' => 'info'
                                                    ];
                                                    $color = $jenisColors[$request->jenis_absen] ?? 'secondary';
                                                @endphp
                                                <span class="badge bg-{{ $color }} px-3 py-2 fw-medium">{{ $request->jenis_absen_text }}</span>
                                            </td>
                                            <td>
                                                @if($request->jam_masuk)
                                                    <div class="d-flex align-items-center mb-1">
                                                        <i class="bi bi-box-arrow-in-right me-2 text-success"></i>
                                                        <span class="fw-medium">{{ $request->jam_masuk }}</span>
                                                    </div>
                                                @endif
                                                @if($request->jam_keluar)
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-box-arrow-right me-2 text-warning"></i>
                                                        <span class="fw-medium">{{ $request->jam_keluar }}</span>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                @if($request->status == 'pending')
                                                    <span class="badge bg-warning px-3 py-2 fw-medium">
                                                        <i class="bi bi-clock me-1"></i>{{ $request->status_text }}
                                                    </span>
                                                @elseif($request->status == 'approved')
                                                    <span class="badge bg-success px-3 py-2 fw-medium">
                                                        <i class="bi bi-check-circle me-1"></i>{{ $request->status_text }}
                                                    </span>
                                                @else
                                                    <span class="badge bg-danger px-3 py-2 fw-medium">
                                                        <i class="bi bi-x-circle me-1"></i>{{ $request->status_text }}
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-clock-history me-2 text-muted"></i>
                                                    <div>
                                                        <div class="fw-medium">{{ $request->created_at->format('d/m/Y') }}</div>
                                                        <small class="text-muted">{{ $request->created_at->format('H:i') }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="pe-4">
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.manual.attendance.requests.show', $request->id) }}"
                                                       class="btn btn-sm btn-outline-primary rounded-pill me-1" title="Lihat Detail">
                                                        <i class="bi bi-eye me-1"></i>Detail
                                                    </a>
                                                    @if($request->status == 'pending')
                                                        <button type="button" class="btn btn-sm btn-outline-success rounded-pill me-1"
                                                                onclick="quickApprove({{ $request->id }})" title="Quick Approve">
                                                            <i class="bi bi-check-circle"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger rounded-pill"
                                                                onclick="quickReject({{ $request->id }})" title="Quick Reject">
                                                            <i class="bi bi-x-circle"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $requests->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="bi bi-inbox display-1 text-muted opacity-50"></i>
                            </div>
                            <h5 class="text-muted mb-2">Tidak Ada Pengajuan</h5>
                            <p class="text-muted">Belum ada pengajuan absen manual yang ditemukan dengan filter yang dipilih.</p>
                            <a href="{{ route('admin.manual.attendance.requests') }}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-clockwise me-2"></i>Reset Filter
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
</div>

<!-- Quick Action Modals -->
<div class="modal fade" id="quickApproveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle me-2"></i>Quick Approve
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="quickApproveForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3" style="width: 50px; height: 50px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-check-circle-fill text-white" style="font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Setujui Pengajuan</h6>
                            <p class="text-muted mb-0">Pengajuan akan disetujui dan absensi akan dibuat otomatis.</p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="quick_approve_notes" class="form-label">Catatan (Opsional)</label>
                        <textarea class="form-control" id="quick_approve_notes" name="admin_notes" rows="3"
                                  placeholder="Tambahkan catatan jika diperlukan..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i>Setujui
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="quickRejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;">
                <h5 class="modal-title">
                    <i class="bi bi-x-circle me-2"></i>Quick Reject
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="quickRejectForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3" style="width: 50px; height: 50px; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-x-circle-fill text-white" style="font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Tolak Pengajuan</h6>
                            <p class="text-muted mb-0">Pengajuan akan ditolak dan tidak akan diproses.</p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="quick_reject_notes" class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="quick_reject_notes" name="admin_notes" rows="3"
                                  placeholder="Jelaskan alasan penolakan..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-x-circle me-1"></i>Tolak
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

function quickApprove(requestId) {
    const form = document.getElementById('quickApproveForm');
    form.action = `/admin/manual-attendance-requests/${requestId}/approve`;
    document.getElementById('quick_approve_notes').value = '';

    const modal = new bootstrap.Modal(document.getElementById('quickApproveModal'));
    modal.show();
}

function quickReject(requestId) {
    const form = document.getElementById('quickRejectForm');
    form.action = `/admin/manual-attendance-requests/${requestId}/reject`;
    document.getElementById('quick_reject_notes').value = '';

    const modal = new bootstrap.Modal(document.getElementById('quickRejectModal'));
    modal.show();
}

// Add loading states to forms
document.getElementById('quickApproveForm').addEventListener('submit', function() {
    showLoading();
});

document.getElementById('quickRejectForm').addEventListener('submit', function() {
    showLoading();
});

// Add loading states to detail buttons
document.querySelectorAll('a[href*="manual-attendance-requests"]').forEach(link => {
    link.addEventListener('click', function() {
        showLoading();
    });
});

// Add smooth animations to table rows
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('.table tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';

        setTimeout(() => {
            row.style.transition = 'all 0.5s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// Auto refresh every 30 seconds for pending requests
@if(request('status') == 'pending' || !request('status'))
setInterval(function() {
    // Only refresh if no modals are open
    if (!document.querySelector('.modal.show')) {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('auto_refresh', '1');

        fetch(currentUrl.toString())
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const newDoc = parser.parseFromString(html, 'text/html');
                const newTable = newDoc.querySelector('.table-responsive');
                const currentTable = document.querySelector('.table-responsive');

                if (newTable && currentTable) {
                    currentTable.innerHTML = newTable.innerHTML;
                }

                // Update statistics
                const newStats = newDoc.querySelectorAll('.stats-card h3');
                const currentStats = document.querySelectorAll('.stats-card h3');

                newStats.forEach((stat, index) => {
                    if (currentStats[index]) {
                        currentStats[index].textContent = stat.textContent;
                    }
                });
            })
            .catch(error => console.log('Auto refresh failed:', error));
    }
}, 30000);
@endif
</script>

@endsection
