<?php

// app/Models/Absensi.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Absensi extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'tanggal',
        'tanggal_mulai',
        'tanggal_selesai',
        'jumlah_hari',
        'pengajuan_id',
        'jam_masuk',
        'jam_keluar',
        'keterangan',
        'status', // hadir, izin, sakit, cuti, dll
        'status_approval', // pending, approved, rejected
        'approval_note',
        'approval_at',
        'approved_by',
        'foto_masuk',
        'foto_keluar',
        'tanda_tangan',
        'dokumen',
        'catatan'
    ];

    protected $casts = [
        'tanggal' => 'date',
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'approval_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function salaryDeductions()
    {
        return $this->hasMany(SalaryDeduction::class, 'absensi_id');
    }
}
