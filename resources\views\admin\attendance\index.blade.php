@extends('layouts.admin')

@section('title', 'Manajemen Kehadiran')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-check me-2"></i>
                            Manajem<PERSON>
                        </h5>
                        <div>
                            <button type="button" class="btn btn-warning btn-sm me-2" data-bs-toggle="modal" data-bs-target="#processModal">
                                <i class="bi bi-gear me-1"></i>
                                Proses <PERSON>
                            </button>
                            <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#bulkProcessModal">
                                <i class="bi bi-calendar-month me-1"></i>
                                Proses <PERSON>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" action="{{ route('admin.attendance.index') }}" class="d-flex gap-2">
                                <input type="date" name="tanggal" class="form-control" value="{{ $tanggal }}">
                                <select name="status" class="form-select">
                                    <option value="">Semua Status</option>
                                    <option value="hadir" {{ $status == 'hadir' ? 'selected' : '' }}>Hadir</option>
                                    <option value="alpha" {{ $status == 'alpha' ? 'selected' : '' }}>Alpha</option>
                                    <option value="izin" {{ $status == 'izin' ? 'selected' : '' }}>Izin</option>
                                    <option value="sakit" {{ $status == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                    <option value="cuti" {{ $status == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                </select>
                                <button type="submit" class="btn btn-outline-info">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ route('admin.attendance.deductions') }}" class="btn btn-danger btn-sm">
                                <i class="bi bi-dash-circle me-1"></i>
                                Kelola Potongan
                            </a>
                        </div>
                    </div>

                    <!-- Karyawan Tanpa Absensi -->
                    @if($usersWithoutAttendance->count() > 0)
                        <div class="alert alert-danger border-0 shadow-sm">
                            <div class="d-flex align-items-center mb-3">
                                <div class="alert-icon me-3">
                                    <i class="bi bi-exclamation-triangle-fill text-danger" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">
                                        🚨 DETEKSI ALPHA - {{ $usersWithoutAttendance->count() }} Karyawan Belum Absen
                                    </h6>
                                    <small class="text-muted">
                                        Tanggal: {{ \Carbon\Carbon::parse($tanggal)->format('d/m/Y') }}
                                        ({{ \Carbon\Carbon::parse($tanggal)->locale('id')->dayName }})
                                    </small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <div class="bg-light p-3 rounded">
                                        <h6 class="mb-2"><i class="bi bi-info-circle me-1"></i> Aturan Alpha:</h6>
                                        <ul class="mb-0 small">
                                            <li><strong>Potongan:</strong> Rp {{ number_format(\App\Models\Setting::getValue('potongan_alpha', 100000)) }} per hari</li>
                                            <li><strong>SP 1:</strong> {{ \App\Models\Setting::getValue('batas_alpha_sp1', 3) }}x alpha dalam sebulan</li>
                                            <li><strong>SP 2:</strong> {{ \App\Models\Setting::getValue('batas_alpha_sp2', 5) }}x alpha dalam sebulan</li>
                                            <li><strong>SP 3:</strong> {{ \App\Models\Setting::getValue('batas_alpha_sp3', 7) }}x alpha dalam sebulan</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button type="button" class="btn btn-warning btn-sm mb-2" onclick="autoMarkAlpha()">
                                        <i class="bi bi-robot me-1"></i>
                                        Auto Mark Alpha
                                    </button>
                                    <br>
                                    <small class="text-muted">Tandai semua sebagai alpha otomatis</small>
                                </div>
                            </div>

                            <form method="POST" action="{{ route('admin.attendance.mark-alpha') }}" id="alphaForm" class="mt-3">
                                @csrf
                                <input type="hidden" name="tanggal" value="{{ $tanggal }}">

                                <div class="row">
                                    @foreach($usersWithoutAttendance as $user)
                                        <div class="col-md-4 col-lg-3 mb-2">
                                            <div class="card border-0 bg-light">
                                                <div class="card-body p-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="user_ids[]"
                                                               value="{{ $user->id }}" id="user{{ $user->id }}">
                                                        <label class="form-check-label w-100" for="user{{ $user->id }}">
                                                            <div>
                                                                <div class="fw-bold small">{{ $user->name }}</div>
                                                                <small class="text-muted">{{ $user->jabatan }}</small>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <input type="text" name="keterangan" class="form-control"
                                               placeholder="Keterangan alpha (opsional)"
                                               value="Tidak hadir tanpa keterangan">
                                    </div>
                                    <div class="col-md-6">
                                        <button type="submit" class="btn btn-danger"
                                                onclick="return confirm('⚠️ KONFIRMASI ALPHA\n\nTindakan ini akan:\n✓ Menandai karyawan sebagai ALPHA\n✓ Memotong gaji Rp ' + {{ \App\Models\Setting::getValue('potongan_alpha', 100000) }} + ' per orang\n✓ Menghitung akumulasi untuk SP\n\nLanjutkan?')">
                                            <i class="bi bi-x-circle me-1"></i>
                                            Tandai Sebagai Alpha
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="selectAll()">
                                            <i class="bi bi-check-all me-1"></i>
                                            Pilih Semua
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    @else
                        <div class="alert alert-success border-0 shadow-sm">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-check-circle-fill text-success me-3" style="font-size: 1.5rem;"></i>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">✅ Semua Karyawan Sudah Absen</h6>
                                    <small class="text-muted">
                                        Tidak ada karyawan yang alpha pada tanggal {{ \Carbon\Carbon::parse($tanggal)->format('d/m/Y') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Tabel Kehadiran -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Karyawan</th>
                                    <th>Tanggal</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Keluar</th>
                                    <th>Status</th>
                                    <th>Keterangan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($absensis as $index => $absensi)
                                    <tr>
                                        <td>{{ $absensis->firstItem() + $index }}</td>
                                        <td>
                                            <div>
                                                <div class="fw-bold">{{ $absensi->user->name }}</div>
                                                <small class="text-muted">{{ $absensi->user->jabatan }}</small>
                                            </div>
                                        </td>
                                        <td>{{ $absensi->tanggal->format('d/m/Y') }}</td>
                                        <td>
                                            @if($absensi->jam_masuk)
                                                {{ \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i') }}
                                                @if($absensi->status == 'hadir')
                                                    @php
                                                        try {
                                                            $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                                            $toleransi = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                                            // Pastikan format jam masuk konsisten
                                                            if (strlen($jamMasuk) == 5) {
                                                                $jamMasuk .= ':00';
                                                            }

                                                            $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                                            $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransi);
                                                            $jamMasukUser = \Carbon\Carbon::parse($absensi->jam_masuk);
                                                            $isLate = $jamMasukUser->gt($batasKeterlambatan);
                                                        } catch (\Exception $e) {
                                                            $isLate = false;
                                                        }
                                                    @endphp
                                                    @if($isLate)
                                                        <br><small class="text-danger">Terlambat</small>
                                                    @else
                                                        <br><small class="text-success">Tepat waktu</small>
                                                    @endif
                                                @endif
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($absensi->jam_keluar)
                                                {{ \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i') }}
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'hadir' => 'success',
                                                    'alpha' => 'danger',
                                                    'izin' => 'info',
                                                    'sakit' => 'warning',
                                                    'cuti' => 'primary'
                                                ];
                                                $color = $statusColors[$absensi->status] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $color }}">{{ ucfirst($absensi->status) }}</span>
                                        </td>
                                        <td>
                                            @if($absensi->keterangan)
                                                <small>{{ $absensi->keterangan }}</small>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <!-- Lihat Detail -->
                                                @if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
                                                    <a href="{{ route('admin.approval.show', $absensi->id) }}"
                                                       class="btn btn-sm btn-outline-info rounded-pill me-1" title="Lihat Detail Pengajuan">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-outline-info rounded-pill me-1"
                                                            onclick="showAbsensiDetail({{ $absensi->id }})" title="Lihat Detail">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                @endif

                                                <!-- Edit (hanya untuk absensi manual/alpha) -->
                                                @if(in_array($absensi->status, ['alpha', 'hadir']) && !$absensi->jam_masuk)
                                                    <button type="button" class="btn btn-sm btn-outline-warning rounded-pill me-1"
                                                            onclick="editAbsensi({{ $absensi->id }})" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                @endif

                                                <!-- Hapus (hanya untuk alpha manual) -->
                                                @if($absensi->status == 'alpha')
                                                    <button type="button" class="btn btn-sm btn-outline-danger rounded-pill"
                                                            onclick="deleteAbsensi({{ $absensi->id }})" title="Hapus">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4"></i>
                                                <p class="mt-2">Tidak ada data kehadiran untuk tanggal ini</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($absensis->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $absensis->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Proses Harian -->
<div class="modal fade" id="processModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.attendance.process-daily') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Proses Kehadiran Harian</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Tanggal</label>
                        <input type="date" name="tanggal" class="form-control" value="{{ $tanggal }}" required>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Proses ini akan:
                        <ul class="mb-0 mt-2">
                            <li>Menandai karyawan yang tidak absen sebagai alpha</li>
                            <li>Membuat potongan gaji untuk keterlambatan</li>
                            <li>Mengeluarkan surat peringatan jika diperlukan</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Proses Sekarang</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Proses Bulanan -->
<div class="modal fade" id="bulkProcessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.attendance.bulk-process') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Proses Kehadiran Bulanan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Bulan</label>
                            <select name="bulan" class="form-select" required>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ date('n') == $i ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Tahun</label>
                            <select name="tahun" class="form-select" required>
                                @for($i = date('Y') - 1; $i <= date('Y'); $i++)
                                    <option value="{{ $i }}" {{ date('Y') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                @endfor
                            </select>
                        </div>
                    </div>
                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Peringatan:</strong> Proses ini akan memproses seluruh bulan dan dapat memakan waktu lama.
                        Pastikan periode yang dipilih sudah benar.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Proses Bulanan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detail Absensi -->
<div class="modal fade" id="detailAbsensiModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">Detail Absensi</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailAbsensiContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Absensi -->
<div class="modal fade" id="editAbsensiModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="editAbsensiForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">Edit Absensi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="editAbsensiContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function deleteAbsensi(id) {
    if (confirm('Hapus record absensi ini?\n\nTindakan ini akan:\n✓ Menghapus data absensi\n✓ Menghapus potongan gaji terkait (jika ada)\n\nTindakan ini tidak dapat dibatalkan!')) {
        // Create form to delete
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/attendance/${id}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
    });
}

function autoMarkAlpha() {
    if (confirm('🤖 AUTO MARK ALPHA\n\nSistem akan otomatis:\n✓ Pilih semua karyawan yang belum absen\n✓ Tandai sebagai ALPHA\n✓ Terapkan potongan gaji\n✓ Hitung akumulasi SP\n\nLanjutkan proses otomatis?')) {
        // Select all checkboxes
        const checkboxes = document.querySelectorAll('input[name="user_ids[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        // Set automatic description
        const keteranganInput = document.querySelector('input[name="keterangan"]');
        if (keteranganInput) {
            keteranganInput.value = 'Tidak hadir tanpa keterangan';
        }

        // Submit form
        document.getElementById('alphaForm').submit();
    }
}

function showAbsensiDetail(id) {
    fetch(`/admin/attendance/${id}/detail`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('detailAbsensiContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('detailAbsensiModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Gagal memuat detail absensi');
        });
}

function editAbsensi(id) {
    fetch(`/admin/attendance/${id}/edit`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('editAbsensiContent').innerHTML = html;
            document.getElementById('editAbsensiForm').action = `/admin/attendance/${id}`;
            new bootstrap.Modal(document.getElementById('editAbsensiModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Gagal memuat form edit');
        });
}
</script>
@endsection
