<?php
// app/Models/User.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'nik',
        'jabatan',
        'departemen',
        'no_hp',
        'alamat',
        'jenis_kelamin',
        'tanggal_lahir',
        'tanggal_bergabung',
        'foto_profil',
        'gaji_pokok',
        'tunjangan_transport',
        'tunjangan_makan',
        'tunjangan_lainnya',
        'jumlah_sp',
        'tanggal_sp_terakhir'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected $casts = [
        'gaji_pokok' => 'decimal:2',
        'tunjangan_transport' => 'decimal:2',
        'tunjangan_makan' => 'decimal:2',
        'tunjangan_lainnya' => 'decimal:2',
    ];

    public function absensi()
    {
        return $this->hasMany(Absensi::class);
    }

    public function monthlySalaries()
    {
        return $this->hasMany(MonthlySalary::class);
    }

    public function overtimeRecords()
    {
        return $this->hasMany(OvertimeRecord::class);
    }

    public function salaryDeductions()
    {
        return $this->hasMany(SalaryDeduction::class);
    }

    // Accessor untuk total gaji
    public function getTotalGajiAttribute()
    {
        return $this->gaji_pokok + $this->tunjangan_transport + $this->tunjangan_makan + $this->tunjangan_lainnya;
    }

    // Method untuk menghitung upah per jam
    public function getUpahPerJamAttribute()
    {
        return $this->gaji_pokok / (22 * 8); // 22 hari kerja, 8 jam per hari
    }

    // Safe accessor untuk tanggal lahir
    public function getTanggalLahirFormattedAttribute()
    {
        try {
            return $this->tanggal_lahir ? \Carbon\Carbon::parse($this->tanggal_lahir)->format('d/m/Y') : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    // Safe accessor untuk tanggal bergabung
    public function getTanggalBergabungFormattedAttribute()
    {
        try {
            return $this->tanggal_bergabung ? \Carbon\Carbon::parse($this->tanggal_bergabung)->format('d/m/Y') : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    // Safe accessor untuk tanggal SP terakhir
    public function getTanggalSpTerakhirFormattedAttribute()
    {
        try {
            return $this->tanggal_sp_terakhir ? \Carbon\Carbon::parse($this->tanggal_sp_terakhir)->format('d/m/Y') : null;
        } catch (\Exception $e) {
            return null;
        }
    }
}
