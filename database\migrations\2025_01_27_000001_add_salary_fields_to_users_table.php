<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('gaji_pokok', 15, 2)->default(3000000)->after('foto_profil');
            $table->decimal('tunjangan_transport', 15, 2)->default(0)->after('gaji_pokok');
            $table->decimal('tunjangan_makan', 15, 2)->default(0)->after('tunjangan_transport');
            $table->decimal('tunjangan_lainnya', 15, 2)->default(0)->after('tunjangan_makan');
            $table->integer('jumlah_sp')->default(0)->after('tunjangan_lainnya');
            $table->date('tanggal_sp_terakhir')->nullable()->after('jumlah_sp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'gaji_pokok',
                'tunjangan_transport',
                'tunjangan_makan',
                'tunjangan_lainnya',
                'jumlah_sp',
                'tanggal_sp_terakhir'
            ]);
        });
    }
};
