<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SalaryDeduction;
use App\Models\Absensi;

class CleanInvalidDeductions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deductions:clean-invalid {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean invalid salary deductions for approved leave (izin, cuti, sakit)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('🔍 Scanning for invalid salary deductions...');
        
        // Cari potongan gaji yang terkait dengan absensi izin/cuti/sakit yang disetujui
        $invalidDeductions = SalaryDeduction::with(['absensi'])
            ->whereHas('absensi', function($query) {
                $query->whereIn('status', ['izin', 'cuti', 'sakit'])
                      ->where('status_approval', 'approved');
            })
            ->whereIn('jenis', ['alpha', 'terlambat'])
            ->get();

        if ($invalidDeductions->isEmpty()) {
            $this->info('✅ No invalid deductions found. All salary deductions are correct.');
            return 0;
        }

        $this->warn("Found {$invalidDeductions->count()} invalid deductions:");
        
        $totalAmount = 0;
        foreach ($invalidDeductions as $deduction) {
            $absensi = $deduction->absensi;
            $user = $deduction->user;
            
            $this->line("  - {$user->name}: {$deduction->jenis} deduction (Rp " . number_format($deduction->jumlah_potongan) . ") for {$absensi->status} on {$deduction->tanggal}");
            $totalAmount += $deduction->jumlah_potongan;
        }
        
        $this->line("  Total invalid amount: Rp " . number_format($totalAmount));
        
        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE: No changes were made. Use without --dry-run to actually clean the data.');
            return 0;
        }
        
        if (!$this->confirm('Do you want to delete these invalid deductions?')) {
            $this->info('Operation cancelled.');
            return 0;
        }
        
        $deletedCount = 0;
        foreach ($invalidDeductions as $deduction) {
            $deduction->delete();
            $deletedCount++;
        }
        
        $this->info("✅ Successfully deleted {$deletedCount} invalid salary deductions.");
        $this->info("💰 Total amount cleaned: Rp " . number_format($totalAmount));
        
        return 0;
    }
}
