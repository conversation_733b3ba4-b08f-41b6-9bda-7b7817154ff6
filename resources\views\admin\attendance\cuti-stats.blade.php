@extends('layouts.admin')

@section('title', 'Monitor <PERSON>')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-check me-2"></i>
                            Monitor Cut<PERSON>
                        </h5>
                        <div>
                            <a href="{{ route('admin.attendance.deductions') }}" class="btn btn-outline-light btn-sm">
                                <i class="bi bi-arrow-left me-1"></i>
                                Kembali ke Potongan
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" action="{{ route('admin.attendance.cuti-stats') }}" class="d-flex gap-2">
                                <select name="tahun" class="form-select" style="min-width: 120px;">
                                    @for($i = date('Y') - 1; $i <= date('Y'); $i++)
                                        <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                                <select name="user_id" class="form-select" style="min-width: 200px;">
                                    <option value="">Semua Karyawan</option>
                                    @foreach(\App\Models\User::where('role', 'user')->orderBy('name')->get() as $user)
                                        <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <button type="submit" class="btn btn-outline-success">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <button type="button" class="btn btn-danger" onclick="exportToPDF()">
                                <i class="bi bi-file-earmark-pdf me-1"></i>
                                Cetak PDF
                            </button>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div class="alert alert-success border-0 shadow-sm mb-4">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-info-circle-fill me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <h6 class="alert-heading mb-1 fw-bold">📋 Kebijakan Cuti Tahunan</h6>
                                <ul class="mb-0 small">
                                    <li><strong>Jatah Cuti:</strong> 12 hari per tahun untuk setiap karyawan</li>
                                    <li><strong>Tidak Dipotong Gaji:</strong> Cuti yang disetujui tidak mengurangi gaji</li>
                                    <li><strong>Approval Required:</strong> Semua cuti harus mendapat persetujuan admin</li>
                                    <li><strong>Carry Over:</strong> Sisa cuti tidak dapat dibawa ke tahun berikutnya</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Statistik Cuti -->
                    <div class="row mb-4">
                        @php
                            $totalKaryawan = count($cutiStats);
                            $karyawanMelebihi = collect($cutiStats)->where('status', 'exceeded')->count();
                            $totalCutiTerpakai = collect($cutiStats)->sum('cuti_terpakai');
                            $totalSisaCuti = collect($cutiStats)->sum('sisa_cuti');
                        @endphp

                        <div class="col-md-3">
                            <div class="card border-0 bg-primary text-white shadow-sm">
                                <div class="card-body text-center">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0">Total Karyawan</h6>
                                        <i class="bi bi-people-fill fs-4"></i>
                                    </div>
                                    <h4 class="mb-1">{{ $totalKaryawan }}</h4>
                                    <small class="opacity-75">Karyawan aktif</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-success text-white shadow-sm">
                                <div class="card-body text-center">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0">Cuti Terpakai</h6>
                                        <i class="bi bi-calendar-check-fill fs-4"></i>
                                    </div>
                                    <h4 class="mb-1">{{ $totalCutiTerpakai }}</h4>
                                    <small class="opacity-75">Total hari</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-info text-white shadow-sm">
                                <div class="card-body text-center">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0">Sisa Cuti</h6>
                                        <i class="bi bi-calendar-plus-fill fs-4"></i>
                                    </div>
                                    <h4 class="mb-1">{{ $totalSisaCuti }}</h4>
                                    <small class="opacity-75">Total hari</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-warning text-white shadow-sm">
                                <div class="card-body text-center">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0">Melebihi Batas</h6>
                                        <i class="bi bi-exclamation-triangle-fill fs-4"></i>
                                    </div>
                                    <h4 class="mb-1">{{ $karyawanMelebihi }}</h4>
                                    <small class="opacity-75">Karyawan</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabel Monitor Cuti -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Karyawan</th>
                                    <th>Jatah Cuti</th>
                                    <th>Cuti Terpakai</th>
                                    <th>Sisa Cuti</th>
                                    <th>Persentase</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($cutiStats as $index => $stat)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <div>
                                                <div class="fw-bold">{{ $stat['user']->name }}</div>
                                                <small class="text-muted">{{ $stat['user']->jabatan }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary rounded-pill">12 hari</span>
                                        </td>
                                        <td>
                                            <span class="fw-bold {{ $stat['cuti_terpakai'] > 12 ? 'text-danger' : 'text-success' }}">
                                                {{ $stat['cuti_terpakai'] }} hari
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold {{ $stat['sisa_cuti'] < 0 ? 'text-danger' : 'text-info' }}">
                                                {{ $stat['sisa_cuti'] }} hari
                                            </span>
                                        </td>
                                        <td>
                                            @php
                                                $persentase = ($stat['cuti_terpakai'] / 12) * 100;
                                                $persentase = min($persentase, 100);
                                            @endphp
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar {{ $persentase > 100 ? 'bg-danger' : ($persentase > 80 ? 'bg-warning' : 'bg-success') }}"
                                                     role="progressbar" style="width: {{ $persentase }}%">
                                                    {{ round($persentase) }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($stat['status'] == 'exceeded')
                                                <span class="badge bg-danger">Melebihi Batas</span>
                                            @elseif($stat['sisa_cuti'] <= 2)
                                                <span class="badge bg-warning">Hampir Habis</span>
                                            @else
                                                <span class="badge bg-success">Normal</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.rekap.izin') }}?user_id={{ $stat['user']->id }}&jenis=cuti&tahun={{ $tahun }}"
                                                   class="btn btn-sm btn-outline-info" title="Lihat Riwayat Cuti">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="showCutiDetail({{ $stat['user']->id }})" title="Detail Cuti">
                                                    <i class="bi bi-calendar-event"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4"></i>
                                                <p class="mt-2">Tidak ada data karyawan</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showCutiDetail(userId) {
    // Simple alert for now - can be expanded to modal
    alert('Detail cuti untuk User ID: ' + userId + '\n\nFitur detail akan dikembangkan lebih lanjut.');
}

function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.location.href = '{{ route("admin.attendance.cuti-stats") }}?' + params.toString();
}
</script>
@endsection
