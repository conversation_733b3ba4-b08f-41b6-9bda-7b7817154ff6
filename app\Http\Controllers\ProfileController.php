<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ProfileController extends Controller
{
    /**
     * Menampilkan halaman profil pengguna
     */
    public function show()
    {
        $user = auth()->user();
        return view('user.profile.show', compact('user'));
    }

    /**
     * Menampilkan form edit profil
     */
    public function edit()
    {
        $user = auth()->user();
        return view('user.profile.edit', compact('user'));
    }

    /**
     * Menyimpan perubahan profil
     */
    public function update(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'nik' => 'nullable|string|max:20',
            'jabatan' => 'nullable|string|max:100',
            'departemen' => 'nullable|string|max:100',
            'no_hp' => 'nullable|string|max:15',
            'alamat' => 'nullable|string',
            'jenis_kelamin' => 'nullable|in:L,P',
            'tanggal_lahir' => 'nullable|date',
            'tanggal_bergabung' => 'nullable|date',
            'foto_profil' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $data = $request->except(['foto_profil', '_token', '_method']);

        // Handle foto profil jika ada
        if ($request->hasFile('foto_profil')) {
            $foto = $request->file('foto_profil');
            $fotoName = time() . '_' . $user->id . '.' . $foto->getClientOriginalExtension();

            // Pastikan direktori ada
            $directory = public_path('foto_profil');
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // Pindahkan file
            $foto->move($directory, $fotoName);

            // Hapus foto lama jika ada
            if ($user->foto_profil && file_exists(public_path($user->foto_profil))) {
                unlink(public_path($user->foto_profil));
            }

            $data['foto_profil'] = 'foto_profil/' . $fotoName;
        }

        $user->update($data);

        return redirect()->route('profile.show')->with('success', 'Profil berhasil diperbarui');
    }

    /**
     * Menampilkan form ganti password
     */
    public function editPassword()
    {
        return view('user.profile.change-password');
    }

    /**
     * Menyimpan password baru
     */
    public function updatePassword(Request $request)
    {
        try {
            $request->validate([
                'current_password' => 'required|current_password',
                'password' => [
                    'required',
                    'string',
                    'min:8',
                    'confirmed',
                    'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/'
                ],
            ], [
                'current_password.current_password' => 'Password saat ini tidak sesuai.',
                'password.min' => 'Password baru minimal 8 karakter.',
                'password.confirmed' => 'Konfirmasi password tidak cocok.',
                'password.regex' => 'Password harus mengandung huruf besar, huruf kecil, dan angka.',
            ]);

            $user = auth()->user();

            // Cek apakah password baru sama dengan password lama
            if (Hash::check($request->password, $user->password)) {
                return redirect()->back()->with('error', 'Password baru tidak boleh sama dengan password lama.');
            }

            $user->update([
                'password' => Hash::make($request->password),
            ]);

            return redirect()->route('profile.show')->with('success', '🎉 Password berhasil diperbarui! Silakan login ulang dengan password baru.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }
}
