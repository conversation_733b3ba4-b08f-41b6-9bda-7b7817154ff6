@extends('layouts.app')

@section('styles')
<style>
    .table th {
        background: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
        padding: 12px;
    }
    .table td {
        padding: 12px;
        vertical-align: middle;
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 16px 20px;
    }
    .btn-primary {
        background: #0d6efd;
        border-color: #0d6efd;
    }
    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
    }
    .form-control, .form-select {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px 12px;
    }
    .form-control:focus, .form-select:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">Data Absensi Pegawai</h4>
                            <small class="text-muted">{{ \App\Facades\Tanggal::formatTanggalLengkap($tanggalFilter) }}</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-success btn-sm me-2" onclick="openAbsenManualModal()">
                                <i class="bi bi-plus-circle me-1"></i>Absen Manual
                            </button>
                            <button class="btn btn-primary btn-sm me-2" onclick="exportToPDF()">
                                <i class="bi bi-file-earmark-pdf me-1"></i>Cetak PDF
                            </button>
                            <a href="{{ route('admin.rekap') }}" class="btn btn-primary btn-sm me-2">
                                <i class="bi bi-file-earmark-text me-1"></i>Rekap
                            </a>
                            <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-arrow-left me-1"></i>Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
                        </div>
                    @endif

                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('admin.absensi') }}" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">Tanggal</label>
                                    <input type="date" name="tanggal" class="form-control" value="{{ request('tanggal', date('Y-m-d')) }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Status</label>
                                    <select name="status" class="form-select">
                                        <option value="">Semua Status</option>
                                        <option value="hadir" {{ request('status') == 'hadir' ? 'selected' : '' }}>Hadir</option>
                                        <option value="alpha" {{ request('status') == 'alpha' ? 'selected' : '' }}>Alpha</option>
                                        <option value="izin" {{ request('status') == 'izin' ? 'selected' : '' }}>Izin</option>
                                        <option value="sakit" {{ request('status') == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                        <option value="cuti" {{ request('status') == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Pegawai</label>
                                    <select name="user_id" class="form-select">
                                        <option value="">Semua Pegawai</option>
                                        @foreach(\App\Models\User::where('role', 'user')->orderBy('name')->get() as $user)
                                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="bi bi-search me-1"></i>Filter
                                    </button>
                                    <a href="{{ route('admin.absensi') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Reset
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center bg-success text-white">
                                <div class="card-body">
                                    <h4>{{ $totalHadir }}</h4>
                                    <p class="mb-0">Hadir</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-danger text-white">
                                <div class="card-body">
                                    <h4>{{ $totalAlpha }}</h4>
                                    <p class="mb-0">Alpha</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-warning text-white">
                                <div class="card-body">
                                    <h4>{{ $totalIzin }}</h4>
                                    <p class="mb-0">Izin/Sakit/Cuti</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-primary text-white">
                                <div class="card-body">
                                    <h4>{{ $totalPegawai }}</h4>
                                    <p class="mb-0">Total Pegawai</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table Section -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Pegawai</th>
                                    <th>Tanggal</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Keluar</th>
                                    <th>Status</th>
                                    <th>Keterangan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($absen as $index => $a)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>
                                            <strong>{{ $a->user->name }}</strong>
                                            @if($a->user->nik)
                                                <br><small class="text-muted">NIK: {{ $a->user->nik }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            {{ \App\Facades\Tanggal::formatTanggal($a->tanggal) }}
                                            <br><small class="text-muted">{{ \App\Facades\Tanggal::formatHari($a->tanggal) }}</small>
                                        </td>
                                        <td>
                                            @if(in_array($a->status, ['izin', 'sakit', 'cuti']))
                                                <span class="text-muted">-</span>
                                            @elseif($a->jam_masuk)
                                                {{ \Carbon\Carbon::parse($a->jam_masuk)->format('H:i') }}
                                                @php
                                                    try {
                                                        $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                                        $toleransi = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                                        // Pastikan format jam masuk konsisten
                                                        if (strlen($jamMasuk) == 5) {
                                                            $jamMasuk .= ':00';
                                                        }

                                                        $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                                        $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransi);
                                                        $jamMasukUser = \Carbon\Carbon::parse($a->jam_masuk);
                                                        $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                                    } catch (\Exception $e) {
                                                        $isTerlambat = false;
                                                    }
                                                @endphp
                                                @if($isTerlambat)
                                                    @php
                                                        try {
                                                            $selisihMenit = $jamMasukUser->diffInMinutes($jamMasukDateTime);
                                                        } catch (\Exception $e) {
                                                            $selisihMenit = 0;
                                                        }
                                                    @endphp
                                                    <br><small class="text-danger">Terlambat {{ $selisihMenit }} menit</small>
                                                @else
                                                    <br><small class="text-success">Tepat waktu</small>
                                                @endif
                                            @else
                                                <span class="text-muted">Belum absen</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if(in_array($a->status, ['izin', 'sakit', 'cuti']))
                                                <span class="text-muted">-</span>
                                            @elseif($a->jam_keluar)
                                                {{ \Carbon\Carbon::parse($a->jam_keluar)->format('H:i') }}
                                                @if($a->jam_masuk)
                                                    @php
                                                        $jamMasuk = \Carbon\Carbon::parse($a->jam_masuk);
                                                        $jamKeluar = \Carbon\Carbon::parse($a->jam_keluar);
                                                        $durasi = $jamKeluar->diff($jamMasuk);
                                                    @endphp
                                                    <br><small class="text-info">{{ $durasi->h }}j {{ $durasi->i }}m</small>
                                                @endif
                                            @else
                                                <span class="text-muted">Belum pulang</span>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'hadir' => 'success',
                                                    'alpha' => 'danger',
                                                    'izin' => 'warning',
                                                    'sakit' => 'info',
                                                    'cuti' => 'primary'
                                                ];
                                                $color = $statusColors[$a->status] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $color }} status-badge">
                                                {{ ucfirst($a->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($a->keterangan)
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ $a->keterangan }}">
                                                    {{ $a->keterangan }}
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="bi bi-calendar-x fs-1 text-muted"></i>
                                            <h5 class="text-muted mt-2">Tidak ada data absensi</h5>
                                            <p class="text-muted">Data absensi untuk tanggal yang dipilih tidak ditemukan</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($absen->hasPages())
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted small">
                                Menampilkan {{ $absen->firstItem() }} - {{ $absen->lastItem() }} dari {{ $absen->total() }} data
                            </div>
                            <div>
                                {{ $absen->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail Absensi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Modal Absen Manual -->
<div class="modal fade" id="absenManualModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.absen.manual') }}">
                @csrf
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>Absen Manual Karyawan
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Absen Manual</strong> - Gunakan fitur ini untuk mencatat absensi karyawan yang tidak bisa melakukan absen otomatis.
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label">Pilih Karyawan <span class="text-danger">*</span></label>
                            <select class="form-select" id="user_id" name="user_id" required>
                                <option value="">-- Pilih Karyawan --</option>
                                @foreach(\App\Models\User::where('role', 'user')->orderBy('name')->get() as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }} - {{ $user->nik ?? 'No NIK' }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="tanggal" class="form-label">Tanggal <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tanggal" name="tanggal" value="{{ date('Y-m-d') }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status Absensi <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required onchange="toggleTimeFields()">
                                <option value="">-- Pilih Status --</option>
                                <option value="hadir">Hadir</option>
                                <option value="izin">Izin</option>
                                <option value="sakit">Sakit</option>
                                <option value="cuti">Cuti</option>
                                <option value="alpha">Alpha</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="keterangan" class="form-label">Keterangan</label>
                            <input type="text" class="form-control" id="keterangan" name="keterangan" placeholder="Keterangan tambahan (opsional)">
                        </div>
                    </div>

                    <div id="timeFields" style="display: none;">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Perhatian:</strong> Pastikan jam keluar lebih besar dari jam masuk.
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="jam_masuk" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-clock"></i></span>
                                    <input type="text"
                                           class="form-control time-input"
                                           id="jam_masuk"
                                           name="jam_masuk"
                                           value="{{ \App\Models\Setting::getValue('jam_masuk', '08:00') }}"
                                           placeholder="08:30"
                                           pattern="[0-9]{1,2}:[0-9]{2}"
                                           maxlength="5"
                                           title="Format: HH:MM (contoh: 08:30)">
                                </div>
                                <div class="form-text">Contoh: 08:30, 09:15, 07:45</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="jam_keluar" class="form-label">Jam Keluar <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-clock"></i></span>
                                    <input type="text"
                                           class="form-control time-input"
                                           id="jam_keluar"
                                           name="jam_keluar"
                                           value="{{ \App\Models\Setting::getValue('jam_pulang', '17:00') }}"
                                           placeholder="17:30"
                                           pattern="[0-9]{1,2}:[0-9]{2}"
                                           maxlength="5"
                                           title="Format: HH:MM (contoh: 17:30)">
                                </div>
                                <div class="form-text">Contoh: 17:30, 18:00, 16:45</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i>Simpan Absensi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showDetail(id) {
    // Show modal with absensi details
    $('#detailModal').modal('show');
    $('#detailContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');

    // You can add AJAX call here to load detail data
    setTimeout(() => {
        $('#detailContent').html('<p>Detail absensi akan ditampilkan di sini.</p>');
    }, 1000);
}

function openAbsenManualModal() {
    const modal = new bootstrap.Modal(document.getElementById('absenManualModal'));
    modal.show();
}

function toggleTimeFields() {
    const status = document.getElementById('status').value;
    const timeFields = document.getElementById('timeFields');

    if (status === 'hadir') {
        timeFields.style.display = 'block';
        document.getElementById('jam_masuk').required = true;
        document.getElementById('jam_keluar').required = true;
    } else {
        timeFields.style.display = 'none';
        document.getElementById('jam_masuk').required = false;
        document.getElementById('jam_keluar').required = false;
        document.getElementById('jam_masuk').value = '';
        document.getElementById('jam_keluar').value = '';
    }
}

// Format input time otomatis
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format time inputs
    const timeInputs = document.querySelectorAll('.time-input');
    timeInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d]/g, ''); // Hapus semua kecuali angka

            if (value.length >= 3) {
                // Format otomatis: 1234 -> 12:34
                value = value.substring(0, 2) + ':' + value.substring(2, 4);
            }

            e.target.value = value;
        });

        input.addEventListener('blur', function(e) {
            let value = e.target.value;

            // Auto-complete format jika user hanya input jam
            if (value.length === 1 || value.length === 2) {
                value = value.padStart(2, '0') + ':00';
            } else if (value.length === 4 && value.indexOf(':') === -1) {
                // Format 0830 -> 08:30
                value = value.substring(0, 2) + ':' + value.substring(2);
            } else if (value.length === 3 && value.indexOf(':') === -1) {
                // Format 830 -> 08:30
                value = '0' + value.substring(0, 1) + ':' + value.substring(1);
            }

            // Validasi format akhir
            const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
            if (value && !timeRegex.test(value)) {
                e.target.classList.add('is-invalid');
                e.target.title = 'Format tidak valid. Gunakan HH:MM (contoh: 08:30)';
            } else {
                e.target.classList.remove('is-invalid');
                e.target.title = 'Format: HH:MM (contoh: 08:30)';
            }

            e.target.value = value;
        });

        // Prevent non-numeric input except colon
        input.addEventListener('keypress', function(e) {
            const char = String.fromCharCode(e.which);
            if (!/[\d:]/.test(char)) {
                e.preventDefault();
            }
        });
    });

    // Validasi form sebelum submit
    const form = document.querySelector('#absenManualModal form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const status = document.getElementById('status').value;
            const jamMasuk = document.getElementById('jam_masuk').value;
            const jamKeluar = document.getElementById('jam_keluar').value;

            // Validasi untuk status hadir
            if (status === 'hadir') {
                if (!jamMasuk || !jamKeluar) {
                    e.preventDefault();
                    alert('Jam masuk dan jam keluar harus diisi untuk status hadir.');
                    return false;
                }

                // Validasi format jam - pastikan format HH:MM
                const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;

                // Normalize format jam (tambahkan 0 di depan jika perlu)
                let normalizedJamMasuk = jamMasuk;
                let normalizedJamKeluar = jamKeluar;

                if (jamMasuk.length === 4 && jamMasuk.indexOf(':') === 1) {
                    normalizedJamMasuk = '0' + jamMasuk;
                    document.getElementById('jam_masuk').value = normalizedJamMasuk;
                }

                if (jamKeluar.length === 4 && jamKeluar.indexOf(':') === 1) {
                    normalizedJamKeluar = '0' + jamKeluar;
                    document.getElementById('jam_keluar').value = normalizedJamKeluar;
                }

                if (!timeRegex.test(normalizedJamMasuk)) {
                    e.preventDefault();
                    alert('Format jam masuk tidak valid. Gunakan format HH:MM (contoh: 08:30).');
                    return false;
                }

                if (!timeRegex.test(normalizedJamKeluar)) {
                    e.preventDefault();
                    alert('Format jam keluar tidak valid. Gunakan format HH:MM (contoh: 17:30).');
                    return false;
                }

                // Validasi jam keluar harus setelah jam masuk
                const masuk = new Date('2000-01-01 ' + normalizedJamMasuk);
                const keluar = new Date('2000-01-01 ' + normalizedJamKeluar);

                if (keluar <= masuk) {
                    e.preventDefault();
                    alert('Jam keluar harus setelah jam masuk.');
                    return false;
                }
            }
        });
    }
});

function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    const url = '{{ route("admin.absensi") }}?' + params.toString() + '&export=pdf';

    // Show loading
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Memproses...';
    btn.disabled = true;

    // Create a temporary link to download
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button after 2 seconds
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

// Auto refresh setiap 30 detik jika tidak ada filter
@if(!request()->hasAny(['tanggal', 'status', 'user_id']))
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
@endif
</script>
@endsection
