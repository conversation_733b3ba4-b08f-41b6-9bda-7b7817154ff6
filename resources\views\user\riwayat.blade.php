@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Riwayat Absensi</h5>
                    <div>
                        <a href="{{ url('/dashboard') }}" class="btn btn-sm btn-light">
                            <i class="bi bi-arrow-left me-1"></i> Kembali
                        </a>
                    </div>
                </div>

                <div class="card-body p-4">
                    <!-- Filter Section -->
                    <div class="card mb-4 bg-light border-0">
                        <div class="card-body">
                            <form action="{{ url('/riwayat') }}" method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label for="bulan" class="form-label">Bulan</label>
                                    <select name="bulan" id="bulan" class="form-select">
                                        <option value="">Semua Bulan</option>
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ request('bulan') == $i ? 'selected' : '' }}>
                                                {{ date('F', mktime(0, 0, 0, $i, 1)) }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="tahun" class="form-label">Tahun</label>
                                    <select name="tahun" id="tahun" class="form-select">
                                        <option value="">Semua Tahun</option>
                                        @for($i = date('Y'); $i >= date('Y')-5; $i--)
                                            <option value="{{ $i }}" {{ request('tahun') == $i ? 'selected' : '' }}>
                                                {{ $i }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="bi bi-filter me-1"></i> Filter
                                    </button>
                                    <a href="{{ url('/riwayat') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-repeat me-1"></i> Reset
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th width="15%">Tanggal</th>
                                    <th width="12%">Status</th>
                                    <th width="12%">Approval</th>
                                    <th width="12%">Jam Masuk</th>
                                    <th width="12%">Jam Keluar</th>
                                    <th width="12%">Foto</th>
                                    <th width="25%">Keterangan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($data as $absen)
                                    <tr>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">
                                                    @php
                                                        try {
                                                            echo \Carbon\Carbon::parse($absen->tanggal)->format('d/m/Y');
                                                        } catch (\Exception $e) {
                                                            echo $absen->tanggal ?? 'N/A';
                                                        }
                                                    @endphp
                                                </span>
                                                <small class="text-muted">
                                                    @php
                                                        try {
                                                            echo \Carbon\Carbon::parse($absen->tanggal)->locale('id')->dayName;
                                                        } catch (\Exception $e) {
                                                            echo '';
                                                        }
                                                    @endphp
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                try {
                                                    $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                                    $toleransiKeterlambatan = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');
                                                    $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                                    $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);

                                                    $isTerlambat = false;
                                                    if ($absen->jam_masuk) {
                                                        $jamMasukUser = \Carbon\Carbon::parse($absen->jam_masuk);
                                                        $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                                    }
                                                } catch (\Exception $e) {
                                                    $isTerlambat = false;
                                                }
                                            @endphp

                                            @if($isTerlambat)
                                                <span class="badge bg-warning">Terlambat</span>
                                            @else
                                                <span class="badge bg-success">Tepat Waktu</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-muted">-</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-clock me-2 text-success"></i>
                                                @php
                                                    try {
                                                        echo $absen->jam_masuk ? \Carbon\Carbon::parse($absen->jam_masuk)->format('H:i:s') : '-';
                                                    } catch (\Exception $e) {
                                                        echo $absen->jam_masuk ?? '-';
                                                    }
                                                @endphp
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-clock-history me-2 text-danger"></i>
                                                @php
                                                    try {
                                                        echo $absen->jam_keluar ? \Carbon\Carbon::parse($absen->jam_keluar)->format('H:i:s') : '-';
                                                    } catch (\Exception $e) {
                                                        echo $absen->jam_keluar ?? '-';
                                                    }
                                                @endphp
                                            </div>
                                        </td>
                                        <td>
                                            @if($absen->foto_masuk || $absen->foto_keluar)
                                                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#fotoModal{{ $absen->id }}">
                                                    <i class="bi bi-camera me-1"></i> Lihat Foto
                                                </button>

                                                <!-- Modal Foto -->
                                                <div class="modal fade" id="fotoModal{{ $absen->id }}" tabindex="-1" aria-labelledby="fotoModalLabel{{ $absen->id }}" aria-hidden="true">
                                                    <div class="modal-dialog modal-lg">
                                                        <div class="modal-content">
                                                            <div class="modal-header bg-primary text-white">
                                                                <h5 class="modal-title" id="fotoModalLabel{{ $absen->id }}">
                                                                    <i class="bi bi-camera me-2"></i>Foto Absensi -
                                                                    @php
                                                                        try {
                                                                            echo \Carbon\Carbon::parse($absen->tanggal)->format('d/m/Y');
                                                                        } catch (\Exception $e) {
                                                                            echo $absen->tanggal ?? 'N/A';
                                                                        }
                                                                    @endphp
                                                                </h5>
                                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body p-4">
                                                                <div class="row">
                                                                    @if($absen->foto_masuk)
                                                                        <div class="col-md-6 mb-3">
                                                                            <div class="card h-100 border-0 shadow-sm">
                                                                                <div class="card-header bg-success text-white py-2">
                                                                                    <h6 class="mb-0">Foto Masuk</h6>
                                                                                </div>
                                                                                <div class="card-body text-center p-3">
                                                                                    <img src="{{ asset($absen->foto_masuk) }}" alt="Foto Masuk" class="img-fluid rounded" style="max-height: 300px;">
                                                                                    <p class="mt-2 mb-0">
                                                                                        <i class="bi bi-clock me-1"></i>
                                                                                        @php
                                                                            try {
                                                                                echo $absen->jam_masuk ? \Carbon\Carbon::parse($absen->jam_masuk)->format('H:i:s') : '-';
                                                                            } catch (\Exception $e) {
                                                                                echo $absen->jam_masuk ?? '-';
                                                                            }
                                                                        @endphp
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endif

                                                                    @if($absen->foto_keluar)
                                                                        <div class="col-md-6 mb-3">
                                                                            <div class="card h-100 border-0 shadow-sm">
                                                                                <div class="card-header bg-danger text-white py-2">
                                                                                    <h6 class="mb-0">Foto Keluar</h6>
                                                                                </div>
                                                                                <div class="card-body text-center p-3">
                                                                                    <img src="{{ asset($absen->foto_keluar) }}" alt="Foto Keluar" class="img-fluid rounded" style="max-height: 300px;">
                                                                                    <p class="mt-2 mb-0">
                                                                                        <i class="bi bi-clock me-1"></i>
                                                                                        @php
                                                                            try {
                                                                                echo $absen->jam_keluar ? \Carbon\Carbon::parse($absen->jam_keluar)->format('H:i:s') : '-';
                                                                            } catch (\Exception $e) {
                                                                                echo $absen->jam_keluar ?? '-';
                                                                            }
                                                                        @endphp
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                </div>

                                                                @if($absen->tanda_tangan)
                                                                    <div class="mt-3">
                                                                        <div class="card border-0 shadow-sm">
                                                                            <div class="card-header bg-secondary text-white py-2">
                                                                                <h6 class="mb-0">Tanda Tangan Digital</h6>
                                                                            </div>
                                                                            <div class="card-body text-center p-3">
                                                                                <img src="{{ asset($absen->tanda_tangan) }}" alt="Tanda Tangan" class="img-fluid rounded" style="max-height: 150px;">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                @endif

                                                                @if($absen->dokumen)
                                                                    <div class="mt-3">
                                                                        <div class="card border-0 shadow-sm">
                                                                            <div class="card-header bg-info text-white py-2">
                                                                                <h6 class="mb-0">Dokumen Pendukung</h6>
                                                                            </div>
                                                                            <div class="card-body p-3">
                                                                                @php
                                                                                    $extension = pathinfo($absen->dokumen, PATHINFO_EXTENSION);
                                                                                    $isPdf = strtolower($extension) === 'pdf';
                                                                                @endphp

                                                                                @if($isPdf)
                                                                                    <div class="d-flex align-items-center">
                                                                                        <i class="bi bi-file-earmark-pdf fs-1 text-danger me-3"></i>
                                                                                        <div>
                                                                                            <p class="mb-1">Dokumen PDF</p>
                                                                                            <div class="btn-group">
                                                                                                <a href="{{ asset('storage/' . $absen->dokumen) }}" class="btn btn-sm btn-primary" target="_blank">
                                                                                                    <i class="bi bi-eye"></i> Lihat
                                                                                                </a>
                                                                                                <a href="{{ asset('storage/' . $absen->dokumen) }}" class="btn btn-sm btn-success" download>
                                                                                                    <i class="bi bi-download"></i> Unduh
                                                                                                </a>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                @else
                                                                                    <div class="text-center">
                                                                                        <img src="{{ asset('storage/' . $absen->dokumen) }}" alt="Dokumen Pendukung" class="img-fluid rounded" style="max-height: 300px;">
                                                                                        <div class="mt-2">
                                                                                            <a href="{{ asset('storage/' . $absen->dokumen) }}" class="btn btn-sm btn-success" download>
                                                                                                <i class="bi bi-download"></i> Unduh Gambar
                                                                                            </a>
                                                                                        </div>
                                                                                    </div>
                                                                                @endif
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                @endif
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($absen->keterangan)
                                                <div class="text-truncate" style="max-width: 250px;" title="{{ $absen->keterangan }}">
                                                    <i class="bi bi-chat-text text-info me-1"></i>
                                                    {{ $absen->keterangan }}
                                                </div>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="bi bi-calendar-x fs-1 text-muted mb-2"></i>
                                                <h5>Tidak ada data absensi</h5>
                                                <p class="text-muted">Belum ada riwayat absensi yang tersedia</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if(method_exists($data, 'links'))
                    <div class="d-flex justify-content-center mt-4">
                        {{ $data->withQueryString()->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    font-weight: 600;
}
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Aktifkan semua tooltip
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection
