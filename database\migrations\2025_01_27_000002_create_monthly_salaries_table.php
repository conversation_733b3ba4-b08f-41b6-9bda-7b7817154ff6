<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monthly_salaries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('bulan'); // 1-12
            $table->integer('tahun');
            
            // Gaji dan tunjangan
            $table->decimal('gaji_pokok', 15, 2);
            $table->decimal('tunjangan_transport', 15, 2)->default(0);
            $table->decimal('tunjangan_makan', 15, 2)->default(0);
            $table->decimal('tunjangan_lainnya', 15, 2)->default(0);
            
            // Lembur
            $table->decimal('total_jam_lembur', 8, 2)->default(0);
            $table->decimal('upah_lembur_per_jam', 15, 2)->default(0);
            $table->decimal('total_upah_lembur', 15, 2)->default(0);
            
            // Kehadiran
            $table->integer('total_hadir')->default(0);
            $table->integer('total_terlambat')->default(0);
            $table->integer('total_alpha')->default(0);
            $table->integer('total_izin')->default(0);
            $table->integer('total_sakit')->default(0);
            $table->integer('total_cuti')->default(0);
            
            // Potongan
            $table->decimal('potongan_terlambat', 15, 2)->default(0);
            $table->decimal('potongan_alpha', 15, 2)->default(0);
            $table->decimal('potongan_sp', 15, 2)->default(0);
            $table->decimal('potongan_lainnya', 15, 2)->default(0);
            
            // Total
            $table->decimal('total_pendapatan', 15, 2)->default(0);
            $table->decimal('total_potongan', 15, 2)->default(0);
            $table->decimal('gaji_bersih', 15, 2)->default(0);
            
            // Status dan catatan
            $table->enum('status', ['draft', 'approved', 'paid'])->default('draft');
            $table->text('catatan')->nullable();
            $table->timestamp('tanggal_dibuat')->useCurrent();
            $table->timestamp('tanggal_disetujui')->nullable();
            $table->timestamp('tanggal_dibayar')->nullable();
            $table->unsignedBigInteger('dibuat_oleh')->nullable();
            $table->unsignedBigInteger('disetujui_oleh')->nullable();
            
            $table->timestamps();
            
            // Index untuk pencarian cepat
            $table->unique(['user_id', 'bulan', 'tahun']);
            $table->index(['bulan', 'tahun']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monthly_salaries');
    }
};
