<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Pengaturan jam kerja
        Setting::setValue('jam_masuk', '08:00:00', 'Jam masuk kerja (format: HH:MM:SS)');
        Setting::setValue('jam_pulang', '17:00:00', 'Jam pulang kerja (format: HH:MM:SS)');
        Setting::setValue('toleransi_keterlambatan', '15', 'Toleransi keterlambatan dalam menit');

        // Pengaturan hari kerja (1=<PERSON>in, 7=Minggu)
        Setting::setValue('hari_kerja', '1,2,3,4,5', '<PERSON> kerja (1=<PERSON><PERSON>, 7=<PERSON>gu, dipisahkan dengan koma)');

        // Pengaturan lainnya
        Setting::setValue('nama_perusahaan', 'PT. Absensi Indonesia', '<PERSON><PERSON> per<PERSON>');
        Setting::setValue('alama<PERSON>_perusahaan', 'Jl. Contoh No. 123, Jakarta', '<PERSON><PERSON><PERSON> perusahaan');
        Setting::setValue('logo_perusahaan', 'logo.png', 'Logo perusahaan');
        Setting::setValue('warna_tema', '#4e73df', 'Warna tema aplikasi');

        // Pengaturan gaji dan lembur
        Setting::setValue('gaji_pokok_default', '3000000', 'Gaji pokok default (Rupiah)');
        Setting::setValue('rate_lembur', '1.5', 'Rate lembur (kelipatan dari gaji per jam)');
        Setting::setValue('potongan_terlambat', '50000', 'Potongan per keterlambatan (Rupiah)');
        Setting::setValue('potongan_alpha', '100000', 'Potongan per hari alpha (Rupiah)');
        Setting::setValue('batas_terlambat_sp1', '3', 'Batas keterlambatan untuk SP 1');
        Setting::setValue('batas_terlambat_sp2', '5', 'Batas keterlambatan untuk SP 2');
        Setting::setValue('batas_terlambat_sp3', '7', 'Batas keterlambatan untuk SP 3');
        Setting::setValue('batas_alpha_sp1', '3', 'Batas alpha untuk SP 1');
        Setting::setValue('batas_alpha_sp2', '5', 'Batas alpha untuk SP 2');
        Setting::setValue('batas_alpha_sp3', '7', 'Batas alpha untuk SP 3');
        Setting::setValue('potongan_sp1', '100000', 'Potongan gaji SP 1 (Rupiah)');
        Setting::setValue('potongan_sp2', '200000', 'Potongan gaji SP 2 (Rupiah)');
        Setting::setValue('potongan_sp3', '500000', 'Potongan gaji SP 3 (Rupiah)');
        Setting::setValue('skip_weekend_attendance', 'true', 'Skip weekend untuk pengecekan alpha');
    }
}
