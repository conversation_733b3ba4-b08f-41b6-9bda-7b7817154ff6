<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            // Mengubah kolom jam_keluar agar bisa menerima nilai NULL
            $table->time('jam_keluar')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            // Mengembalikan kolom jam_keluar ke kondisi semula (tidak nullable)
            $table->time('jam_keluar')->nullable(false)->change();
        });
    }
};
