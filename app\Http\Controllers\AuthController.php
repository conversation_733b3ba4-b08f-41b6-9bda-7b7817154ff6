<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class AuthController extends Controller
{
    public function showLogin()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'password' => 'required'
            ]);

            $credentials = $request->only('email', 'password');

            // Debugging
            Log::info('Login attempt', ['email' => $request->email]);

            // Cek apakah email ada di database
            $user = User::where('email', $request->email)->first();
            if (!$user) {
                Log::warning('Login failed - Email not found', ['email' => $request->email]);
                return back()->withErrors(['email' => 'Email tidak terdaftar'])->withInput($request->only('email'));
            }

            // Cek password
            if (!Hash::check($request->password, $user->password)) {
                Log::warning('Login failed - Invalid password', ['email' => $request->email]);
                return back()->withErrors(['password' => 'Password salah'])->withInput($request->only('email'));
            }

            // Login dengan Auth facade
            if (Auth::attempt($credentials, $request->filled('remember'))) {
                $request->session()->regenerate();

                // Debugging
                Log::info('Login successful', [
                    'user_id' => Auth::id(),
                    'user_name' => Auth::user()->name,
                    'user_role' => Auth::user()->role
                ]);

                // Redirect berdasarkan role pengguna
                $role = Auth::user()->role;
                Log::info('User role', ['role' => $role]);

                if ($role === 'admin') {
                    Log::info('Redirecting to admin dashboard');
                    return redirect('/admin/dashboard')->with('success', 'Login berhasil sebagai admin');
                } else {
                    Log::info('Redirecting to user dashboard');
                    return redirect('/dashboard')->with('success', 'Login berhasil sebagai user');
                }
            }

            Log::warning('Login failed - Auth attempt failed', ['email' => $request->email]);
            return back()->withErrors(['email' => 'Terjadi kesalahan saat login. Silakan coba lagi.'])->withInput($request->only('email'));

        } catch (\Illuminate\Session\TokenMismatchException $e) {
            Log::warning('Login failed - CSRF token mismatch', [
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);
            return redirect()->route('login')->with('error', 'Sesi Anda telah berakhir. Silakan login kembali.');

        } catch (\Exception $e) {
            // Check if it's a CSRF token error (419)
            if ($e instanceof \Symfony\Component\HttpKernel\Exception\HttpException && $e->getStatusCode() === 419) {
                Log::warning('Login failed - CSRF token expired', ['email' => $request->email ?? 'unknown']);
                return redirect()->route('login')->with('error', 'Sesi Anda telah berakhir. Silakan login kembali.');
            }

            Log::error('Login error', [
                'email' => $request->email ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withErrors(['email' => 'Terjadi kesalahan sistem. Silakan coba lagi.'])->withInput($request->only('email'));
        }
    }

    public function showRegister()
    {
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'email' => 'required|email|unique:users',
            'password' => 'required|confirmed|min:6',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'user',
        ]);

        // Redirect ke login dengan pesan sukses, tidak langsung login
        return redirect()->route('login')->with('success', 'Registrasi berhasil! Silakan login dengan akun yang baru dibuat.');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/login');
    }
}
