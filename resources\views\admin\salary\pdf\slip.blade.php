<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Slip <PERSON> - {{ $salary->user->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .company-address {
            font-size: 11px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .document-title {
            font-size: 16px;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .employee-info {
            margin-bottom: 20px;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .info-table td {
            padding: 5px;
            border: none;
        }
        
        .info-table .label {
            width: 150px;
            font-weight: bold;
        }
        
        .salary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .salary-table th,
        .salary-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        
        .salary-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        
        .amount {
            text-align: right;
        }
        
        .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        
        .net-salary {
            font-size: 14px;
            font-weight: bold;
            background-color: #e8f5e8;
        }
        
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 60px;
            padding-top: 5px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .status-draft {
            background-color: #ffc107;
            color: #000;
        }
        
        .status-approved {
            background-color: #28a745;
            color: #fff;
        }
        
        .status-paid {
            background-color: #007bff;
            color: #fff;
        }
        
        .print-info {
            font-size: 10px;
            color: #666;
            text-align: right;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $namaPerusahaan }}</div>
        <div class="company-address">{{ $alamatPerusahaan }}</div>
        <div class="document-title">SLIP GAJI KARYAWAN</div>
    </div>

    <!-- Employee Information -->
    <div class="employee-info">
        <table class="info-table">
            <tr>
                <td class="label">Nama Karyawan</td>
                <td>: {{ $salary->user->name }}</td>
                <td class="label">Periode</td>
                <td>: {{ $salary->periode }}</td>
            </tr>
            <tr>
                <td class="label">NIK</td>
                <td>: {{ $salary->user->nik ?? '-' }}</td>
                <td class="label">Status</td>
                <td>: 
                    <span class="status-badge status-{{ $salary->status }}">
                        {{ $salary->status_text }}
                    </span>
                </td>
            </tr>
            <tr>
                <td class="label">Jabatan</td>
                <td>: {{ $salary->user->jabatan ?? '-' }}</td>
                <td class="label">Tanggal Cetak</td>
                <td>: {{ date('d/m/Y H:i:s') }}</td>
            </tr>
            <tr>
                <td class="label">Departemen</td>
                <td>: {{ $salary->user->departemen ?? '-' }}</td>
                <td class="label">Dibuat Oleh</td>
                <td>: {{ $salary->creator->name ?? 'System' }}</td>
            </tr>
        </table>
    </div>

    <!-- Salary Details -->
    <table class="salary-table">
        <thead>
            <tr>
                <th style="width: 60%;">KETERANGAN</th>
                <th style="width: 40%;">JUMLAH (Rp)</th>
            </tr>
        </thead>
        <tbody>
            <!-- Pendapatan -->
            <tr>
                <td colspan="2" style="background-color: #e8f5e8; font-weight: bold; text-align: center;">
                    PENDAPATAN
                </td>
            </tr>
            <tr>
                <td>Gaji Pokok</td>
                <td class="amount">{{ number_format($salary->gaji_pokok, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Tunjangan Transport</td>
                <td class="amount">{{ number_format($salary->tunjangan_transport, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Tunjangan Makan</td>
                <td class="amount">{{ number_format($salary->tunjangan_makan, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Tunjangan Lainnya</td>
                <td class="amount">{{ number_format($salary->tunjangan_lainnya, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Upah Lembur ({{ $salary->total_jam_lembur }} jam)</td>
                <td class="amount">{{ number_format($salary->total_upah_lembur, 0, ',', '.') }}</td>
            </tr>
            <tr class="total-row">
                <td>TOTAL PENDAPATAN</td>
                <td class="amount">{{ number_format($salary->total_pendapatan, 0, ',', '.') }}</td>
            </tr>

            <!-- Potongan -->
            <tr>
                <td colspan="2" style="background-color: #ffe8e8; font-weight: bold; text-align: center;">
                    POTONGAN
                </td>
            </tr>
            <tr>
                <td>Potongan Alpha</td>
                <td class="amount">{{ number_format($salary->potongan_alpha, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Potongan Terlambat</td>
                <td class="amount">{{ number_format($salary->potongan_terlambat, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Potongan SP</td>
                <td class="amount">{{ number_format($salary->potongan_sp, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Potongan Lainnya</td>
                <td class="amount">{{ number_format($salary->potongan_lainnya, 0, ',', '.') }}</td>
            </tr>
            <tr class="total-row">
                <td>TOTAL POTONGAN</td>
                <td class="amount">{{ number_format($salary->total_potongan, 0, ',', '.') }}</td>
            </tr>

            <!-- Gaji Bersih -->
            <tr class="net-salary">
                <td>GAJI BERSIH</td>
                <td class="amount">{{ number_format($salary->gaji_bersih, 0, ',', '.') }}</td>
            </tr>
        </tbody>
    </table>

    <!-- Attendance Summary -->
    <div style="margin-bottom: 20px;">
        <h4 style="margin-bottom: 10px;">Ringkasan Kehadiran:</h4>
        <table class="info-table">
            <tr>
                <td class="label">Total Hari Kerja</td>
                <td>: {{ $salary->total_hari_kerja }} hari</td>
                <td class="label">Total Hadir</td>
                <td>: {{ $salary->total_hadir }} hari</td>
            </tr>
            <tr>
                <td class="label">Total Alpha</td>
                <td>: {{ $salary->total_alpha }} hari</td>
                <td class="label">Total Terlambat</td>
                <td>: {{ $salary->total_terlambat }} hari</td>
            </tr>
            <tr>
                <td class="label">Total Izin/Sakit/Cuti</td>
                <td>: {{ $salary->total_izin }} hari</td>
                <td class="label">Total Jam Lembur</td>
                <td>: {{ $salary->total_jam_lembur }} jam</td>
            </tr>
        </table>
    </div>

    <!-- Footer with Signatures -->
    <div class="footer">
        <div class="signature">
            <div>Karyawan</div>
            <div class="signature-line">{{ $salary->user->name }}</div>
        </div>
        
        <div class="signature">
            <div>HRD</div>
            <div class="signature-line">{{ $salary->approver->name ?? '________________' }}</div>
        </div>
        
        <div class="signature">
            <div>Direktur</div>
            <div class="signature-line">________________</div>
        </div>
    </div>

    <!-- Print Info -->
    <div class="print-info">
        Dokumen ini digenerate secara otomatis oleh sistem pada {{ date('d/m/Y H:i:s') }}
    </div>
</body>
</html>
