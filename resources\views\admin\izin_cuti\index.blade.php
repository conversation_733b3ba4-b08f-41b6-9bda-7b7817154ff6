@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center rounded-top">
            <h5 class="mb-0"><i class="bi bi-person-badge-fill me-2"></i>Manajemen Izin / Cuti / Sakit</h5>
            <a href="{{ route('admin.dashboard') }}" class="btn btn-sm btn-outline-light">
                <i class="bi bi-arrow-left-circle"></i> Kembali ke Dashboard
            </a>
        </div>
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            {{-- Hidden Status Filter --}}
            @if(request('status'))
                <input type="hidden" id="current_status" value="{{ request('status') }}">
            @endif

            {{-- Filter Form --}}
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h6 class="mb-0 fw-bold"><i class="bi bi-funnel me-2"></i>Filter Data</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.izin-cuti.index') }}" method="GET">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="jenis" class="form-label fw-semibold">Jenis</label>
                                <select name="jenis" id="jenis" class="form-select">
                                    <option value="">Semua Jenis</option>
                                    <option value="izin" {{ request('jenis') == 'izin' ? 'selected' : '' }}>Izin</option>
                                    <option value="sakit" {{ request('jenis') == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                    <option value="cuti" {{ request('jenis') == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label fw-semibold">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">Semua Status</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Disetujui</option>
                                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Ditolak</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="user_id" class="form-label fw-semibold">Pegawai</label>
                                <select name="user_id" id="user_id" class="form-select">
                                    <option value="">Semua Pegawai</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="tanggal_mulai" class="form-label fw-semibold">Tanggal Mulai</label>
                                <input type="date" name="tanggal_mulai" id="tanggal_mulai" class="form-control" value="{{ request('tanggal_mulai') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="tanggal_selesai" class="form-label fw-semibold">Tanggal Selesai</label>
                                <input type="date" name="tanggal_selesai" id="tanggal_selesai" class="form-control" value="{{ request('tanggal_selesai') }}">
                            </div>
                        </div>
                        <div class="mt-3 d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-funnel-fill me-1"></i> Filter
                                </button>
                                <a href="{{ route('admin.izin-cuti.index') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-counterclockwise me-1"></i> Reset
                                </a>
                            </div>
                            <a href="{{ route('admin.izin-cuti.laporan', request()->all()) }}" class="btn btn-success">
                                <i class="bi bi-printer-fill me-1"></i> Cetak Laporan
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            {{-- Table --}}
            <div class="table-responsive">
                <table class="table table-bordered align-middle table-striped">
                    <thead class="table-dark">
                        <tr class="text-center">
                            <th>No</th>
                            <th>Nama</th>
                            <th>Jenis</th>
                            <th>Tanggal</th>
                            <th>Durasi</th>
                            <th>Status</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($izinCutis as $index => $izinCuti)
                            <tr>
                                <td class="text-center">{{ $index + 1 }}</td>
                                <td>{{ $izinCuti->user->name }}</td>
                                <td>
                                    <span class="badge text-capitalize
                                        {{
                                            $izinCuti->jenis == 'izin' ? 'bg-info' :
                                            ($izinCuti->jenis == 'sakit' ? 'bg-warning text-dark' : 'bg-primary')
                                        }}">
                                        {{ $izinCuti->jenis }}
                                    </span>
                                </td>
                                <td>{{ \Carbon\Carbon::parse($izinCuti->tanggal_mulai)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($izinCuti->tanggal_selesai)->format('d/m/Y') }}</td>
                                <td class="text-center">{{ $izinCuti->durasi }} hari</td>
                                <td>
                                    <span class="badge text-capitalize
                                        {{
                                            $izinCuti->status == 'approved' ? 'bg-success' :
                                            ($izinCuti->status == 'rejected' ? 'bg-danger' : 'bg-secondary')
                                        }}">
                                        {{ $izinCuti->status == 'approved' ? 'Disetujui' :
                                           ($izinCuti->status == 'rejected' ? 'Ditolak' : 'Menunggu')
                                        }}
                                    </span>
                                </td>
                                <td>{{ Str::limit($izinCuti->keterangan, 40) }}</td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ route('admin.izin-cuti.cetak', $izinCuti->id) }}" class="btn btn-success" target="_blank" title="Cetak">
                                            <i class="bi bi-printer-fill"></i>
                                        </a>

                                        @if($izinCuti->status == 'pending')
                                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#approvalModal{{ $izinCuti->id }}" title="Tindakan">
                                                <i class="bi bi-check2-square"></i>
                                            </button>

                                            <!-- Modal Approval -->
                                            <div class="modal fade" id="approvalModal{{ $izinCuti->id }}" tabindex="-1" aria-labelledby="approvalModalLabel{{ $izinCuti->id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="approvalModalLabel{{ $izinCuti->id }}">Tindakan Pengajuan</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="mb-3">
                                                                <p><strong>Nama:</strong> {{ $izinCuti->user->name }}</p>
                                                                <p><strong>Jenis:</strong> {{ ucfirst($izinCuti->jenis) }}</p>
                                                                <p><strong>Tanggal:</strong> {{ \Carbon\Carbon::parse($izinCuti->tanggal_mulai)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($izinCuti->tanggal_selesai)->format('d/m/Y') }}</p>
                                                                <p><strong>Keterangan:</strong> {{ $izinCuti->keterangan }}</p>
                                                            </div>

                                                            <form id="approveForm{{ $izinCuti->id }}" action="{{ route('admin.izin-cuti.update', $izinCuti->id) }}" method="POST">
                                                                @csrf
                                                                @method('PUT')
                                                                <input type="hidden" name="status" id="status{{ $izinCuti->id }}" value="">

                                                                <div class="mb-3">
                                                                    <label for="catatan_admin{{ $izinCuti->id }}" class="form-label">Catatan Admin</label>
                                                                    <textarea class="form-control" id="catatan_admin{{ $izinCuti->id }}" name="catatan_admin" rows="3" placeholder="Berikan catatan untuk pengajuan ini (opsional)"></textarea>
                                                                </div>
                                                            </form>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                            <button type="button" class="btn btn-danger" onclick="submitForm('{{ $izinCuti->id }}', 'rejected')">
                                                                <i class="bi bi-x-circle me-1"></i> Tolak
                                                            </button>
                                                            <button type="button" class="btn btn-success" onclick="submitForm('{{ $izinCuti->id }}', 'approved')">
                                                                <i class="bi bi-check-circle me-1"></i> Setujui
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @else
                                            <a href="#" class="btn btn-info" data-bs-toggle="popover" data-bs-trigger="hover" title="{{ $izinCuti->status == 'approved' ? 'Disetujui' : 'Ditolak' }}" data-bs-content="Catatan: {{ $izinCuti->catatan_admin ?: 'Tidak ada catatan' }}">
                                                <i class="bi bi-info-circle"></i>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center text-muted">Tidak ada data pengajuan</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            <div class="d-flex justify-content-center mt-4">
                {{ $izinCutis->withQueryString()->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Inisialisasi popover
    document.addEventListener('DOMContentLoaded', function() {
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    });

    // Fungsi untuk submit form approval/rejection
    function submitForm(id, status) {
        document.getElementById('status' + id).value = status;
        document.getElementById('approveForm' + id).submit();
    }
</script>
@endpush
