<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OvertimeRecord;
use App\Models\User;
use Illuminate\Http\Request;

class OvertimeController extends Controller
{
    public function index(Request $request)
    {
        $bulan = $request->get('bulan', date('n'));
        $tahun = $request->get('tahun', date('Y'));
        $status = $request->get('status');

        $overtimes = OvertimeRecord::with(['user', 'absensi'])
            ->byPeriod($bulan, $tahun)
            ->when($status, function($query) use ($status) {
                return $query->where('status', $status);
            })
            ->orderBy('tanggal', 'desc')
            ->paginate(10);

        return view('admin.overtime.index', compact('overtimes', 'bulan', 'tahun', 'status'));
    }

    public function show($id)
    {
        $overtime = OvertimeRecord::with(['user', 'absensi', 'approver'])->findOrFail($id);

        return view('admin.overtime.show', compact('overtime'));
    }

    public function approve(Request $request, $id)
    {
        $request->validate([
            'catatan_admin' => 'nullable|string|max:500'
        ]);

        $overtime = OvertimeRecord::findOrFail($id);

        $overtime->update([
            'status' => 'approved',
            'catatan_admin' => $request->catatan_admin,
            'tanggal_disetujui' => now(),
            'disetujui_oleh' => auth()->id()
        ]);

        return redirect()->back()->with('success', 'Lembur berhasil disetujui');
    }

    public function reject(Request $request, $id)
    {
        $request->validate([
            'catatan_admin' => 'required|string|max:500'
        ]);

        $overtime = OvertimeRecord::findOrFail($id);

        $overtime->update([
            'status' => 'rejected',
            'catatan_admin' => $request->catatan_admin,
            'tanggal_disetujui' => now(),
            'disetujui_oleh' => auth()->id()
        ]);

        return redirect()->back()->with('success', 'Lembur berhasil ditolak');
    }

    public function bulkApprove(Request $request)
    {
        $request->validate([
            'overtime_ids' => 'required|array',
            'overtime_ids.*' => 'exists:overtime_records,id'
        ]);

        $updated = OvertimeRecord::whereIn('id', $request->overtime_ids)
            ->where('status', 'pending')
            ->update([
                'status' => 'approved',
                'tanggal_disetujui' => now(),
                'disetujui_oleh' => auth()->id()
            ]);

        return redirect()->back()->with('success', "Berhasil menyetujui {$updated} lembur");
    }

    public function generateFromAbsensi(Request $request)
    {
        $request->validate([
            'bulan' => 'required|integer|min:1|max:12',
            'tahun' => 'required|integer|min:2020|max:2030'
        ]);

        $bulan = $request->bulan;
        $tahun = $request->tahun;

        // Ambil semua absensi yang jam keluarnya melebihi jam pulang normal
        $absensis = \App\Models\Absensi::whereMonth('tanggal', $bulan)
            ->whereYear('tanggal', $tahun)
            ->where('status', 'hadir')
            ->whereNotNull('jam_keluar')
            ->get();

        $generated = 0;
        $errors = [];

        foreach ($absensis as $absensi) {
            try {
                // Cek apakah sudah ada record lembur untuk absensi ini
                $existing = OvertimeRecord::where('absensi_id', $absensi->id)->first();
                if ($existing) {
                    continue;
                }

                // Hitung lembur
                $overtimeData = OvertimeRecord::calculateOvertimeFromAbsensi($absensi);

                if ($overtimeData && $overtimeData['jam_lembur'] > 0) {
                    OvertimeRecord::create([
                        'user_id' => $absensi->user_id,
                        'absensi_id' => $absensi->id,
                        'tanggal' => $absensi->tanggal,
                        'jam_pulang_normal' => $overtimeData['jam_pulang_normal'],
                        'jam_keluar_aktual' => $overtimeData['jam_keluar_aktual'],
                        'jam_lembur' => $overtimeData['jam_lembur'],
                        'upah_per_jam' => $overtimeData['upah_per_jam'],
                        'rate_lembur' => $overtimeData['rate_lembur'],
                        'upah_lembur_per_jam' => $overtimeData['upah_lembur_per_jam'],
                        'total_upah_lembur' => $overtimeData['total_upah_lembur'],
                        'keterangan' => 'Lembur dari absensi',
                        'status' => 'pending'
                    ]);

                    $generated++;
                }
            } catch (\Exception $e) {
                $errors[] = "Error untuk absensi ID {$absensi->id}: " . $e->getMessage();
            }
        }

        if ($generated > 0) {
            $message = "Berhasil generate {$generated} record lembur.";
            if (!empty($errors)) {
                $message .= " Dengan " . count($errors) . " error.";
            }
            return redirect()->back()->with('success', $message);
        } else {
            return redirect()->back()->with('info', 'Tidak ada record lembur yang di-generate. ' . implode(' ', $errors));
        }
    }
}
