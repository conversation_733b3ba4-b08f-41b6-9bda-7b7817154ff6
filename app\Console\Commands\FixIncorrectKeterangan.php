<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Absensi;

class FixIncorrectKeterangan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:fix-keterangan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix incorrect keterangan in attendance records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing incorrect keterangan in attendance records...');

        // Update keterangan yang mengandung "Auto-generated" untuk status hadir
        $updatedHadir = Absensi::where('status', 'hadir')
            ->where('keterangan', 'LIKE', '%Auto-generated%')
            ->update(['keterangan' => null]);

        if ($updatedHadir > 0) {
            $this->line("✓ Updated {$updatedHadir} incorrect keterangan for 'hadir' status (set to null)");
        }

        // Update keterangan yang mengandung "Auto-generated" untuk status alpha
        $updatedAlpha = Absensi::where('status', 'alpha')
            ->where('keterangan', 'LIKE', '%Auto-generated%')
            ->update(['keterangan' => 'Tidak hadir tanpa keterangan']);

        if ($updatedAlpha > 0) {
            $this->line("✓ Updated {$updatedAlpha} incorrect keterangan for 'alpha' status");
        }

        // Update keterangan yang mengandung "sistem otomatis" untuk status hadir
        $updatedSistem = Absensi::where('status', 'hadir')
            ->where('keterangan', 'LIKE', '%sistem otomatis%')
            ->update(['keterangan' => null]);

        if ($updatedSistem > 0) {
            $this->line("✓ Updated {$updatedSistem} incorrect 'sistem otomatis' keterangan for 'hadir' status");
        }

        $totalUpdated = $updatedHadir + $updatedAlpha + $updatedSistem;

        if ($totalUpdated > 0) {
            $this->info("✅ Total {$totalUpdated} records updated successfully!");
        } else {
            $this->info("✅ No incorrect keterangan found. All records are clean!");
        }

        return 0;
    }
}
