@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Reka<PERSON><PERSON></h5>
            <div>
                <a href="{{ route('admin.rekap.izin') }}" class="btn btn-warning btn-sm me-2">
                    <i class="bi bi-calendar-minus"></i> Lihat Rekap Izin/Cuti/Sakit
                </a>
                <a href="{{ route('admin.dashboard') }}" class="btn btn-light btn-sm">
                    <i class="bi bi-arrow-left"></i> Kembali ke Dashboard
                </a>
            </div>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.rekap') }}" method="GET" class="mb-4">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="tanggal_mulai" class="form-label">Tang<PERSON>lai</label>
                        <input type="date" name="tanggal_mulai" id="tanggal_mulai" class="form-control" value="{{ request('tanggal_mulai') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="tanggal_akhir" class="form-label">Tanggal Akhir</label>
                        <input type="date" name="tanggal_akhir" id="tanggal_akhir" class="form-control" value="{{ request('tanggal_akhir') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="user_id" class="form-label">Pegawai</label>
                        <select name="user_id" id="user_id" class="form-select">
                            <option value="">Semua Pegawai</option>
                            @foreach($users as $user)
                                <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                    {{ $user->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">Semua Status</option>
                            <option value="tepat waktu" {{ request('status') == 'tepat waktu' ? 'selected' : '' }}>Tepat Waktu</option>
                            <option value="terlambat" {{ request('status') == 'terlambat' ? 'selected' : '' }}>Terlambat</option>
                        </select>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-filter"></i> Filter
                    </button>
                    <a href="{{ route('admin.rekap') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-counterclockwise"></i> Reset
                    </a>
                    <a href="{{ route('admin.cetak.pdf', request()->all()) }}" class="btn btn-success" target="_blank">
                        <i class="bi bi-printer"></i> Cetak Semua
                    </a>
                    @if(request('user_id'))
                        <a href="{{ route('admin.cetak.user.pdf', [request('user_id'), 'tanggal_mulai' => request('tanggal_mulai'), 'tanggal_akhir' => request('tanggal_akhir')]) }}" class="btn btn-info" target="_blank">
                            <i class="bi bi-printer"></i> Cetak Pegawai Terpilih
                        </a>
                    @endif
                </div>
            </form>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>No</th>
                            <th>Nama</th>
                            <th>Tanggal</th>
                            <th>Status</th>
                            <th>Jam Masuk</th>
                            <th>Jam Keluar</th>

                            <th>Foto</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($absensis as $index => $absensi)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $absensi->user->name }}</td>
                                <td>{{ \App\Facades\Tanggal::formatTanggalLengkap($absensi->tanggal) }}</td>
                                <td>
                                    @if($absensi->status == 'hadir')
                                        @php
                                            try {
                                                $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                                $toleransiKeterlambatan = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                                // Pastikan format jam masuk konsisten
                                                if (strlen($jamMasuk) == 5) {
                                                    $jamMasuk .= ':00';
                                                }

                                                $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                                $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);
                                                $jamMasukUser = \Carbon\Carbon::parse($absensi->jam_masuk);
                                                $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                            } catch (\Exception $e) {
                                                $isTerlambat = false;
                                            }
                                        @endphp

                                        @if($isTerlambat)
                                            <span class="badge bg-warning">Terlambat</span>
                                            <small class="d-block text-muted mt-1">
                                                @php
                                                    try {
                                                        // Pastikan format jam masuk konsisten
                                                        $jamMasukForCalc = $jamMasuk;
                                                        if (strlen($jamMasukForCalc) == 5) {
                                                            $jamMasukForCalc .= ':00';
                                                        }

                                                        // Ambil jam masuk dari setting dan jam masuk user
                                                        $jamMasukTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasukForCalc);
                                                        $jamMasukUserTime = \Carbon\Carbon::parse($absensi->jam_masuk);

                                                        // Ekstrak hanya waktu (jam:menit:detik)
                                                        $jamMasukTimeStr = $jamMasukTime->format('H:i:s');
                                                        $jamMasukUserTimeStr = $jamMasukUserTime->format('H:i:s');

                                                        // Buat objek Carbon dengan tanggal yang sama (hari ini)
                                                        $today = \Carbon\Carbon::today()->format('Y-m-d');
                                                        $carbonJamMasuk = \Carbon\Carbon::parse($today . ' ' . $jamMasukTimeStr);
                                                        $carbonJamMasukUser = \Carbon\Carbon::parse($today . ' ' . $jamMasukUserTimeStr);

                                                        // Hitung selisih dalam menit
                                                        if ($carbonJamMasukUser->gt($carbonJamMasuk)) {
                                                            // Gunakan diffInMinutes untuk mendapatkan selisih dalam menit
                                                            $diffMinutes = (int)$carbonJamMasukUser->diffInMinutes($carbonJamMasuk);

                                                            // Konversi ke jam dan menit
                                                            $diffHours = (int)floor($diffMinutes / 60);
                                                            $remainingMinutes = (int)($diffMinutes % 60);

                                                            // Tampilkan dalam format jam dan menit
                                                            if ($diffHours > 0) {
                                                                echo $diffHours . ' jam ' . $remainingMinutes . ' menit';
                                                            } else {
                                                                echo $diffMinutes . ' menit';
                                                            }
                                                        } else {
                                                            echo '0 menit';
                                                        }
                                                    } catch (\Exception $e) {
                                                        echo 'Tidak dapat menghitung keterlambatan';
                                                    }
                                                @endphp
                                            </small>
                                        @else
                                            <span class="badge bg-success">Tepat Waktu</span>
                                        @endif
                                    @else
                                        <span class="badge {{
                                            $absensi->status == 'izin' ? 'bg-warning' :
                                            ($absensi->status == 'sakit' ? 'bg-info' :
                                            ($absensi->status == 'cuti' ? 'bg-primary' : 'bg-danger'))
                                        }}">
                                            {{ ucfirst($absensi->status) }}
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    @if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
                                        <span class="text-muted">-</span>
                                    @else
                                        @php
                                            try {
                                                echo \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i:s');
                                            } catch (\Exception $e) {
                                                echo $absensi->jam_masuk ?? '-';
                                            }
                                        @endphp
                                    @endif
                                </td>
                                <td>
                                    @if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
                                        <span class="text-muted">-</span>
                                    @else
                                        @php
                                            try {
                                                echo $absensi->jam_keluar ? \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i:s') : '-';
                                            } catch (\Exception $e) {
                                                echo $absensi->jam_keluar ?? '-';
                                            }
                                        @endphp
                                    @endif
                                </td>

                                <td>
                                    @if($absensi->foto_masuk || $absensi->foto_keluar)
                                        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#fotoModal{{ $absensi->id }}">
                                            <i class="bi bi-camera"></i> Lihat
                                        </button>

                                        <!-- Modal Foto -->
                                        <div class="modal fade" id="fotoModal{{ $absensi->id }}" tabindex="-1" aria-labelledby="fotoModalLabel{{ $absensi->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="fotoModalLabel{{ $absensi->id }}">Foto Absensi - {{ $absensi->user->name }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            @if($absensi->foto_masuk)
                                                                <div class="col-md-6 mb-3">
                                                                    <div class="card">
                                                                        <div class="card-header bg-primary text-white">
                                                                            <h6 class="mb-0">Foto Masuk</h6>
                                                                        </div>
                                                                        <div class="card-body text-center">
                                                                            <img src="{{ asset($absensi->foto_masuk) }}" alt="Foto Masuk" class="img-fluid border rounded" style="max-height: 300px;">
                                                                            <p class="mt-2 text-muted">Jam:
                                                                                @php
                                                                                    try {
                                                                                        echo $absensi->jam_masuk ? \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i:s') : '-';
                                                                                    } catch (\Exception $e) {
                                                                                        echo $absensi->jam_masuk ?? '-';
                                                                                    }
                                                                                @endphp
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endif

                                                            @if($absensi->foto_keluar)
                                                                <div class="col-md-6 mb-3">
                                                                    <div class="card">
                                                                        <div class="card-header bg-success text-white">
                                                                            <h6 class="mb-0">Foto Keluar</h6>
                                                                        </div>
                                                                        <div class="card-body text-center">
                                                                            <img src="{{ asset($absensi->foto_keluar) }}" alt="Foto Keluar" class="img-fluid border rounded" style="max-height: 300px;">
                                                                            <p class="mt-2 text-muted">Jam:
                                                                                @php
                                                                                    try {
                                                                                        echo $absensi->jam_keluar ? \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i:s') : '-';
                                                                                    } catch (\Exception $e) {
                                                                                        echo $absensi->jam_keluar ?? '-';
                                                                                    }
                                                                                @endphp
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>
                                    @if($absensi->keterangan)
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ $absensi->keterangan }}">
                                            {{ $absensi->keterangan }}
                                        </span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#detailModal{{ $absensi->id }}">
                                            <i class="bi bi-eye"></i> Detail
                                        </button>

                                        @if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
                                            <a href="{{ route('admin.cetak.izin.pdf', $absensi->id) }}" class="btn btn-sm btn-success" target="_blank">
                                                <i class="bi bi-printer"></i> Cetak Surat
                                            </a>
                                        @endif

                                        <a href="{{ route('admin.cetak.user.pdf', [$absensi->user_id, 'tanggal_mulai' => request('tanggal_mulai'), 'tanggal_akhir' => request('tanggal_akhir')]) }}" class="btn btn-sm btn-info" target="_blank">
                                            <i class="bi bi-printer"></i> Cetak Rekap
                                        </a>
                                    </div>

                                    <!-- Modal Detail -->
                                    <div class="modal fade" id="detailModal{{ $absensi->id }}" tabindex="-1" aria-labelledby="detailModalLabel{{ $absensi->id }}" aria-hidden="true">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="detailModalLabel{{ $absensi->id }}">Detail Absensi</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6>Informasi Pegawai</h6>
                                                            <table class="table table-bordered">
                                                                <tr>
                                                                    <th>Nama</th>
                                                                    <td>{{ $absensi->user->name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>Email</th>
                                                                    <td>{{ $absensi->user->email }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>Tanggal</th>
                                                                    <td>{{ \App\Facades\Tanggal::formatTanggalLengkap($absensi->tanggal) }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>Status</th>
                                                                    <td>
                                                                        @if($absensi->status == 'hadir')
                                                                            @php
                                                                                try {
                                                                                    $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                                                                    $toleransiKeterlambatan = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                                                                    // Pastikan format jam masuk konsisten
                                                                                    if (strlen($jamMasuk) == 5) {
                                                                                        $jamMasuk .= ':00';
                                                                                    }

                                                                                    $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                                                                    $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);
                                                                                    $jamMasukUser = \Carbon\Carbon::parse($absensi->jam_masuk);
                                                                                    $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                                                                } catch (\Exception $e) {
                                                                                    $isTerlambat = false;
                                                                                }
                                                                            @endphp

                                                                            @if($isTerlambat)
                                                                                <span class="badge bg-warning">Terlambat</span>
                                                                                <small class="d-block text-muted mt-1">
                                                                                    Terlambat
                                                                                    @php
                                                                                        try {
                                                                                            // Pastikan format jam masuk konsisten
                                                                                            $jamMasukForCalc = $jamMasuk;
                                                                                            if (strlen($jamMasukForCalc) == 5) {
                                                                                                $jamMasukForCalc .= ':00';
                                                                                            }

                                                                                            // Ambil jam masuk dari setting dan jam masuk user
                                                                                            $jamMasukTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasukForCalc);
                                                                                            $jamMasukUserTime = \Carbon\Carbon::parse($absensi->jam_masuk);

                                                                                            // Ekstrak hanya waktu (jam:menit:detik)
                                                                                            $jamMasukTimeStr = $jamMasukTime->format('H:i:s');
                                                                                            $jamMasukUserTimeStr = $jamMasukUserTime->format('H:i:s');

                                                                                            // Buat objek Carbon dengan tanggal yang sama (hari ini)
                                                                                            $today = \Carbon\Carbon::today()->format('Y-m-d');
                                                                                            try {
                                                                                                $carbonJamMasuk = \Carbon\Carbon::parse("{$today} {$jamMasukTimeStr}");
                                                                                                $carbonJamMasukUser = \Carbon\Carbon::parse("{$today} {$jamMasukUserTimeStr}");
                                                                                            } catch (\Exception $e) {
                                                                                                throw new \Exception('Error parsing time');
                                                                                            }

                                                                                            // Hitung selisih dalam menit
                                                                                            if ($carbonJamMasukUser->gt($carbonJamMasuk)) {
                                                                                                // Gunakan diffInMinutes untuk mendapatkan selisih dalam menit
                                                                                                $diffMinutes = (int)$carbonJamMasukUser->diffInMinutes($carbonJamMasuk);

                                                                                                // Konversi ke jam dan menit
                                                                                                $diffHours = (int)floor($diffMinutes / 60);
                                                                                                $remainingMinutes = (int)($diffMinutes % 60);

                                                                                                // Tampilkan dalam format jam dan menit
                                                                                                if ($diffHours > 0) {
                                                                                                    echo "{$diffHours} jam {$remainingMinutes} menit";
                                                                                                } else {
                                                                                                    echo "{$diffMinutes} menit";
                                                                                                }
                                                                                            } else {
                                                                                                echo '0 menit';
                                                                                            }
                                                                                        } catch (\Exception $e) {
                                                                                            echo 'Tidak dapat menghitung keterlambatan';
                                                                                        }
                                                                                    @endphp
                                                                                    dari jam masuk ({{ $jamMasuk }})
                                                                                </small>
                                                                            @else
                                                                                <span class="badge bg-success">Tepat Waktu</span>
                                                                                <small class="d-block text-muted mt-1">Jam masuk: {{ $jamMasuk }} (Toleransi: {{ $toleransiKeterlambatan }} menit)</small>
                                                                            @endif
                                                                        @else
                                                                            <span class="badge {{
                                                                                $absensi->status == 'izin' ? 'bg-warning' :
                                                                                ($absensi->status == 'sakit' ? 'bg-info' :
                                                                                ($absensi->status == 'cuti' ? 'bg-primary' : 'bg-danger'))
                                                                            }}">
                                                                                {{ ucfirst($absensi->status) }}
                                                                            </span>
                                                                        @endif
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <th>Jam Masuk</th>
                                                                    <td>
                                                                        @if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
                                                                            <span class="text-muted">-</span>
                                                                        @else
                                                                            @php
                                                                                try {
                                                                                    echo \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i:s');
                                                                                } catch (\Exception $e) {
                                                                                    echo $absensi->jam_masuk ?? '-';
                                                                                }
                                                                            @endphp
                                                                        @endif
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <th>Jam Keluar</th>
                                                                    <td>
                                                                        @if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
                                                                            <span class="text-muted">-</span>
                                                                        @else
                                                                            @php
                                                                                try {
                                                                                    echo $absensi->jam_keluar ? \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i:s') : '-';
                                                                                } catch (\Exception $e) {
                                                                                    echo $absensi->jam_keluar ?? '-';
                                                                                }
                                                                            @endphp
                                                                        @endif
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <th>Keterangan</th>
                                                                    <td>{{ $absensi->keterangan ?? '-' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6>Foto & Tanda Tangan</h6>
                                                            <div class="mb-3">
                                                                <label class="form-label">Foto Masuk</label>
                                                                <div>
                                                                    @if($absensi->foto_masuk)
                                                                        <img src="{{ asset($absensi->foto_masuk) }}" alt="Foto Masuk" class="img-fluid border" style="max-height: 150px;">
                                                                    @else
                                                                        <p class="text-muted">Tidak ada foto</p>
                                                                    @endif
                                                                </div>
                                                            </div>

                                                            <div class="mb-3">
                                                                <label class="form-label">Foto Keluar</label>
                                                                <div>
                                                                    @if($absensi->foto_keluar)
                                                                        <img src="{{ asset($absensi->foto_keluar) }}" alt="Foto Keluar" class="img-fluid border" style="max-height: 150px;">
                                                                    @else
                                                                        <p class="text-muted">Tidak ada foto</p>
                                                                    @endif
                                                                </div>
                                                            </div>

                                                            <div class="mb-3">
                                                                <label class="form-label">Tanda Tangan</label>
                                                                <div>
                                                                    @if($absensi->tanda_tangan)
                                                                        <img src="{{ asset($absensi->tanda_tangan) }}" alt="Tanda Tangan" class="img-fluid border" style="max-height: 100px;">
                                                                    @else
                                                                        <p class="text-muted">Tidak ada tanda tangan</p>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="text-center">Tidak ada data absensi</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $absensis->appends(request()->all())->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
