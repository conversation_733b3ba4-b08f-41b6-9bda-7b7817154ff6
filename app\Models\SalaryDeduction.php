<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalaryDeduction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'tanggal',
        'bulan',
        'tahun',
        'jenis',
        'jumlah_potongan',
        'keterangan',
        'catatan',
        'absensi_id',
        'data_pendukung',
        'status',
        'tanggal_disetujui',
        'dibuat_oleh',
        'disetujui_oleh'
    ];

    protected $casts = [
        'tanggal' => 'date',
        'tanggal_disetujui' => 'datetime',
        'jumlah_potongan' => 'decimal:2',
        'data_pendukung' => 'array'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function absensi()
    {
        return $this->belongsTo(Absensi::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'dibuat_oleh');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'disetujui_oleh');
    }

    // Accessors
    public function getJenisTextAttribute()
    {
        $texts = [
            'terlambat' => 'Keterlambatan',
            'alpha' => 'Alpha/Tanpa Keterangan',
            'sp1' => 'Surat Peringatan 1',
            'sp2' => 'Surat Peringatan 2',
            'sp3' => 'Surat Peringatan 3',
            'lainnya' => 'Lainnya'
        ];
        
        return $texts[$this->jenis] ?? 'Lainnya';
    }

    public function getJenisBadgeAttribute()
    {
        $badges = [
            'terlambat' => 'bg-warning',
            'alpha' => 'bg-danger',
            'sp1' => 'bg-info',
            'sp2' => 'bg-warning',
            'sp3' => 'bg-danger',
            'lainnya' => 'bg-secondary'
        ];
        
        return $badges[$this->jenis] ?? 'bg-secondary';
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'bg-warning',
            'approved' => 'bg-success',
            'rejected' => 'bg-danger'
        ];
        
        return $badges[$this->status] ?? 'bg-secondary';
    }

    public function getStatusTextAttribute()
    {
        $texts = [
            'pending' => 'Menunggu',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak'
        ];
        
        return $texts[$this->status] ?? 'Menunggu';
    }

    // Methods
    public static function createFromLateness($absensi)
    {
        $potonganTerlambat = (float)Setting::getValue('potongan_terlambat', 50000);
        
        return self::create([
            'user_id' => $absensi->user_id,
            'tanggal' => $absensi->tanggal,
            'bulan' => date('n', strtotime($absensi->tanggal)),
            'tahun' => date('Y', strtotime($absensi->tanggal)),
            'jenis' => 'terlambat',
            'jumlah_potongan' => $potonganTerlambat,
            'keterangan' => 'Potongan keterlambatan pada ' . $absensi->tanggal,
            'absensi_id' => $absensi->id,
            'status' => 'approved',
            'dibuat_oleh' => auth()->id()
        ]);
    }

    public static function createFromAlpha($userId, $tanggal)
    {
        $potonganAlpha = (float)Setting::getValue('potongan_alpha', 100000);
        
        return self::create([
            'user_id' => $userId,
            'tanggal' => $tanggal,
            'bulan' => date('n', strtotime($tanggal)),
            'tahun' => date('Y', strtotime($tanggal)),
            'jenis' => 'alpha',
            'jumlah_potongan' => $potonganAlpha,
            'keterangan' => 'Potongan alpha/tanpa keterangan pada ' . $tanggal,
            'status' => 'approved',
            'dibuat_oleh' => auth()->id()
        ]);
    }

    public static function createFromWarningLetter($userId, $jenisSP)
    {
        $potonganSP = (float)Setting::getValue('potongan_' . $jenisSP, 100000);
        
        return self::create([
            'user_id' => $userId,
            'tanggal' => now()->toDateString(),
            'bulan' => now()->month,
            'tahun' => now()->year,
            'jenis' => $jenisSP,
            'jumlah_potongan' => $potonganSP,
            'keterangan' => 'Potongan ' . strtoupper($jenisSP),
            'status' => 'approved',
            'dibuat_oleh' => auth()->id()
        ]);
    }

    // Scopes
    public function scopeByPeriod($query, $bulan, $tahun)
    {
        return $query->where('bulan', $bulan)->where('tahun', $tahun);
    }

    public function scopeByJenis($query, $jenis)
    {
        return $query->where('jenis', $jenis);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }
}
