@extends('layouts.admin')

@section('title', 'Manajemen Lembur')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-clock me-2"></i>
                            Manajemen Lembur
                        </h5>
                        <button type="button" class="btn btn-dark btn-sm" data-bs-toggle="modal" data-bs-target="#generateModal">
                            <i class="bi bi-plus-circle me-1"></i>
                            Generate Lembur
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" action="{{ route('admin.overtime.index') }}" class="d-flex gap-2">
                                <select name="bulan" class="form-select">
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ $bulan == $i ? 'selected' : '' }}>
                                            {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                        </option>
                                    @endfor
                                </select>
                                <select name="tahun" class="form-select">
                                    @for($i = date('Y') - 2; $i <= date('Y') + 1; $i++)
                                        <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                                <select name="status" class="form-select">
                                    <option value="">Semua Status</option>
                                    <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                    <option value="approved" {{ $status == 'approved' ? 'selected' : '' }}>Disetujui</option>
                                    <option value="rejected" {{ $status == 'rejected' ? 'selected' : '' }}>Ditolak</option>
                                </select>
                                <button type="submit" class="btn btn-outline-warning">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            @if($overtimes->where('status', 'pending')->count() > 0)
                                <form method="POST" action="{{ route('admin.overtime.bulk-approve') }}" class="d-inline">
                                    @csrf
                                    <button type="button" class="btn btn-success btn-sm" onclick="bulkApprove()">
                                        <i class="bi bi-check-all me-1"></i>
                                        Setujui Semua Pending
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>

                    <!-- Tabel Lembur -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>Karyawan</th>
                                    <th>Tanggal</th>
                                    <th>Jam Pulang Normal</th>
                                    <th>Jam Keluar Aktual</th>
                                    <th>Jam Lembur</th>
                                    <th>Upah Lembur</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($overtimes as $index => $overtime)
                                    <tr>
                                        <td>
                                            @if($overtime->status == 'pending')
                                                <input type="checkbox" name="overtime_ids[]" value="{{ $overtime->id }}" class="form-check-input overtime-checkbox">
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($overtime->user->foto_profil)
                                                    <img src="{{ asset('storage/' . $overtime->user->foto_profil) }}" 
                                                         class="rounded-circle me-2" width="32" height="32">
                                                @else
                                                    <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                         style="width: 32px; height: 32px;">
                                                        <i class="bi bi-person text-white"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <div class="fw-bold">{{ $overtime->user->name }}</div>
                                                    <small class="text-muted">{{ $overtime->user->jabatan }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $overtime->tanggal->format('d/m/Y') }}</td>
                                        <td>{{ \Carbon\Carbon::parse($overtime->jam_pulang_normal)->format('H:i') }}</td>
                                        <td>{{ \Carbon\Carbon::parse($overtime->jam_keluar_aktual)->format('H:i') }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ $overtime->jam_lembur_format }}</span>
                                        </td>
                                        <td>
                                            <div>Rate: {{ $overtime->rate_lembur }}x</div>
                                            <small class="text-success fw-bold">Rp {{ number_format($overtime->total_upah_lembur, 0, ',', '.') }}</small>
                                        </td>
                                        <td>
                                            <span class="badge {{ $overtime->status_badge }}">{{ $overtime->status_text }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.overtime.show', $overtime->id) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                @if($overtime->status == 'pending')
                                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                                            onclick="approveOvertime({{ $overtime->id }})">
                                                        <i class="bi bi-check-circle"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="rejectOvertime({{ $overtime->id }})">
                                                        <i class="bi bi-x-circle"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4"></i>
                                                <p class="mt-2">Belum ada data lembur untuk periode ini</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($overtimes->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $overtimes->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Generate Lembur -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.overtime.generate') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Generate Lembur dari Absensi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Bulan</label>
                            <select name="bulan" class="form-select" required>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ date('n') == $i ? 'selected' : '' }}>
                                        {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Tahun</label>
                            <select name="tahun" class="form-select" required>
                                @for($i = date('Y') - 1; $i <= date('Y'); $i++)
                                    <option value="{{ $i }}" {{ date('Y') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                @endfor
                            </select>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Sistem akan menganalisis data absensi dan membuat record lembur otomatis untuk karyawan yang pulang melebihi jam kerja normal.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Generate Lembur</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Select All functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.overtime-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk approve function
function bulkApprove() {
    const checkedBoxes = document.querySelectorAll('.overtime-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Pilih minimal satu lembur untuk disetujui');
        return;
    }
    
    if (confirm(`Setujui ${checkedBoxes.length} lembur yang dipilih?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.overtime.bulk-approve") }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);
        
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'overtime_ids[]';
            input.value = checkbox.value;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Individual approve function
function approveOvertime(id) {
    if (confirm('Setujui lembur ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/overtime/${id}/approve`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Individual reject function
function rejectOvertime(id) {
    const reason = prompt('Alasan penolakan:');
    if (reason) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/overtime/${id}/reject`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);
        
        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'catatan_admin';
        reasonInput.value = reason;
        form.appendChild(reasonInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endsection
