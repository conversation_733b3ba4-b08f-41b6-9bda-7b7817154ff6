<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AbsensiController;
use App\Http\Controllers\IzinCutiController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\IzinCutiController as AdminIzinCutiController;
use App\Http\Controllers\Admin\SalaryController as AdminSalaryController;
use App\Http\Controllers\Admin\OvertimeController as AdminOvertimeController;
use App\Http\Controllers\Admin\AttendanceManagementController;
use App\Http\Controllers\SalaryController;

// Home route
Route::get('/', function () {
    // Jika sudah login, redirect ke dashboard sesuai role
    if (auth()->check()) {
        if (auth()->user()->role === 'admin') {
            return redirect('/admin/dashboard');
        } else {
            return redirect('/dashboard');
        }
    }

    // Jika belum login, tampilkan halaman welcome
    return view('welcome');
})->name('home');

// Auth routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);


});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');

    Route::middleware('admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/absensi', [AdminController::class, 'absensiIndex'])->name('absensi');
        Route::get('/rekap', [AdminController::class, 'rekapAbsensi'])->name('rekap');
        Route::get('/rekap-izin', [AdminController::class, 'rekapIzin'])->name('rekap.izin');

        // Approval Routes
        Route::get('/approval', [AdminController::class, 'approvalIndex'])->name('approval.index');
        Route::get('/approval/{id}', [AdminController::class, 'approvalShow'])->name('approval.show');
        Route::post('/approval/{id}/approve', [AdminController::class, 'approvalApprove'])->name('approval.approve');
        Route::post('/approval/{id}/reject', [AdminController::class, 'approvalReject'])->name('approval.reject');

        // Rute untuk cetak PDF
        Route::get('/cetak-pdf', [AdminController::class, 'cetakPDF'])->name('cetak.pdf');
        Route::get('/cetak-rekap-izin-pdf', [AdminController::class, 'cetakRekapIzinPDF'])->name('cetak.rekap.izin.pdf');

        // Rute untuk cetak PDF per user
        Route::get('/cetak-user-pdf/{userId}', [AdminController::class, 'cetakUserPDF'])->name('cetak.user.pdf');

        // Izin Cuti Routes
        Route::get('/izin-cuti', [AdminIzinCutiController::class, 'index'])->name('izin-cuti.index');
        Route::get('/izin-cuti/{id}', function() {
            return view('errors.pdf_disabled');
        })->name('izin-cuti.show');
        Route::put('/izin-cuti/{id}', [AdminIzinCutiController::class, 'update'])->name('izin-cuti.update');
        // Rute untuk cetak PDF izin cuti
        Route::get('/izin-cuti/{id}/cetak', [AdminIzinCutiController::class, 'cetak'])->name('izin-cuti.cetak');


        Route::get('/izin-cuti-laporan', function() {
            return view('errors.pdf_disabled');
        })->name('izin-cuti.laporan');

        // User Management Routes
        Route::get('/users', [UserManagementController::class, 'index'])->name('users.index');
        Route::get('/users/create', [UserManagementController::class, 'create'])->name('users.create');
        Route::post('/users', [UserManagementController::class, 'store'])->name('users.store');
        Route::get('/users/{id}/edit', [UserManagementController::class, 'edit'])->name('users.edit');
        Route::put('/users/{id}', [UserManagementController::class, 'update'])->name('users.update');
        Route::delete('/users/{id}', [UserManagementController::class, 'destroy'])->name('users.destroy');

        // Pengaturan Sistem
        Route::get('/settings', [AdminController::class, 'showSettings'])->name('settings');
        Route::post('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');

        // Salary Management Routes
        Route::get('/salary', [AdminSalaryController::class, 'index'])->name('salary.index');
        Route::post('/salary/generate', [AdminSalaryController::class, 'generate'])->name('salary.generate');
        Route::get('/salary/{id}', [AdminSalaryController::class, 'show'])->name('salary.show');
        Route::post('/salary/{id}/approve', [AdminSalaryController::class, 'approve'])->name('salary.approve');
        Route::post('/salary/{id}/mark-paid', [AdminSalaryController::class, 'markAsPaid'])->name('salary.mark-paid');
        Route::get('/salary/{id}/print', [AdminSalaryController::class, 'printPdf'])->name('salary.print');

        // Overtime Management Routes
        Route::get('/overtime', [AdminOvertimeController::class, 'index'])->name('overtime.index');
        Route::get('/overtime/{id}', [AdminOvertimeController::class, 'show'])->name('overtime.show');
        Route::post('/overtime/{id}/approve', [AdminOvertimeController::class, 'approve'])->name('overtime.approve');
        Route::post('/overtime/{id}/reject', [AdminOvertimeController::class, 'reject'])->name('overtime.reject');
        Route::post('/overtime/bulk-approve', [AdminOvertimeController::class, 'bulkApprove'])->name('overtime.bulk-approve');
        Route::post('/overtime/generate', [AdminOvertimeController::class, 'generateFromAbsensi'])->name('overtime.generate');

        // Attendance Management Routes
        Route::get('/attendance', [AttendanceManagementController::class, 'index'])->name('attendance.index');
        Route::get('/attendance/{id}/detail', [AttendanceManagementController::class, 'attendanceDetail'])->name('attendance.detail');
        Route::get('/attendance/{id}/edit', [AttendanceManagementController::class, 'attendanceEdit'])->name('attendance.edit');
        Route::put('/attendance/{id}', [AttendanceManagementController::class, 'attendanceUpdate'])->name('attendance.update');
        Route::delete('/attendance/{id}', [AttendanceManagementController::class, 'attendanceDelete'])->name('attendance.delete');
        Route::post('/attendance/mark-alpha', [AttendanceManagementController::class, 'markAsAlpha'])->name('attendance.mark-alpha');
        Route::post('/attendance/process-daily', [AttendanceManagementController::class, 'processDaily'])->name('attendance.process-daily');
        Route::post('/attendance/bulk-process', [AttendanceManagementController::class, 'bulkProcessMonth'])->name('attendance.bulk-process');
        Route::get('/attendance/deductions', [AttendanceManagementController::class, 'deductions'])->name('attendance.deductions');
        Route::get('/attendance/deductions/clean', [AttendanceManagementController::class, 'manualCleanOrphanedDeductions'])->name('attendance.deductions.clean');
        Route::post('/attendance/deductions/clean-invalid', [AttendanceManagementController::class, 'cleanInvalidDeductions'])->name('attendance.deductions.clean-invalid');
        Route::get('/attendance/deductions/{id}/view', [AttendanceManagementController::class, 'viewDeduction'])->name('attendance.deductions.view');
        Route::get('/attendance/deductions/{id}/edit', [AttendanceManagementController::class, 'editDeduction'])->name('attendance.deductions.edit');
        Route::put('/attendance/deductions/{id}', [AttendanceManagementController::class, 'updateDeduction'])->name('attendance.deductions.update');
        Route::patch('/attendance/deductions/{id}/approve', [AttendanceManagementController::class, 'approveDeduction'])->name('attendance.deductions.approve');
        Route::patch('/attendance/deductions/{id}/reject', [AttendanceManagementController::class, 'rejectDeduction'])->name('attendance.deductions.reject');
        Route::delete('/attendance/deductions/{id}', [AttendanceManagementController::class, 'deleteDeduction'])->name('attendance.deductions.delete');
        Route::post('/attendance/deductions/create', [AttendanceManagementController::class, 'createManualDeduction'])->name('attendance.deductions.create');
        Route::get('/attendance/cuti-stats', [AttendanceManagementController::class, 'cutiStats'])->name('attendance.cuti-stats');

        // Manual Attendance Route
        Route::post('/absen/manual', [AdminController::class, 'absenManual'])->name('absen.manual');

        // Manual Attendance Request Management Routes
        Route::get('/manual-attendance-requests', [AdminController::class, 'manualAttendanceRequests'])->name('manual.attendance.requests');
        Route::get('/manual-attendance-requests/{id}', [AdminController::class, 'showManualAttendanceRequest'])->name('manual.attendance.requests.show');
        Route::post('/manual-attendance-requests/{id}/approve', [AdminController::class, 'approveManualAttendanceRequest'])->name('manual.attendance.requests.approve');
        Route::post('/manual-attendance-requests/{id}/reject', [AdminController::class, 'rejectManualAttendanceRequest'])->name('manual.attendance.requests.reject');

    });

    // Route absensi
    Route::get('/absen', [AbsensiController::class, 'formAbsen'])->name('absen.form');
    Route::get('/absen/masuk', [AbsensiController::class, 'formAbsenMasuk'])->name('absen.masuk.form');
    Route::post('/absen/masuk', [AbsensiController::class, 'submitAbsenMasuk'])->name('absen.masuk.submit');
    Route::get('/absen/keluar', [AbsensiController::class, 'formAbsenKeluar'])->name('absen.keluar.form');
    Route::post('/absen/keluar', [AbsensiController::class, 'submitAbsenKeluar'])->name('absen.keluar.submit');
    Route::post('/absen', [AbsensiController::class, 'submitAbsen'])->name('absen.submit'); // Untuk kompatibilitas



    // Route riwayat absensi
    Route::get('/riwayat', [AbsensiController::class, 'riwayat'])->name('absen.riwayat');
    Route::get('/pengajuan', [AbsensiController::class, 'pengajuanRiwayat'])->name('absen.pengajuan');

    // Route izin
    Route::get('/izin', [AbsensiController::class, 'formIzin'])->name('izin.form');
    Route::post('/izin', [AbsensiController::class, 'submitIzin'])->name('izin.submit');
    Route::delete('/izin/{id}', [AbsensiController::class, 'batalkanPengajuan'])->name('izin.batalkan');

    // Route pengajuan absen manual
    Route::post('/absen/manual/request', [AbsensiController::class, 'requestManualAttendance'])->name('user.absen.manual.request');

    // Route profil
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [ProfileController::class, 'editPassword'])->name('profile.password.edit');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');



    // Route izin, cuti, sakit
    Route::get('/izin-cuti', [IzinCutiController::class, 'index'])->name('izin-cuti.index');
    Route::get('/izin-cuti/create', [IzinCutiController::class, 'create'])->name('izin-cuti.create');
    Route::post('/izin-cuti', [IzinCutiController::class, 'store'])->name('izin-cuti.store');
    Route::get('/izin-cuti/{id}', function() {
        return view('errors.pdf_disabled');
    })->name('izin-cuti.show');
    Route::delete('/izin-cuti/{id}', [IzinCutiController::class, 'destroy'])->name('izin-cuti.destroy');

    // Route salary untuk user
    Route::get('/salary', [SalaryController::class, 'index'])->name('salary.index');
    Route::get('/salary/{id}', [SalaryController::class, 'show'])->name('salary.show');
    Route::get('/salary/{id}/print', [SalaryController::class, 'printPdf'])->name('salary.print');
    Route::get('/salary/overtime/history', [SalaryController::class, 'overtime'])->name('salary.overtime');
    Route::get('/salary/deductions/history', [SalaryController::class, 'deductions'])->name('salary.deductions');
});
