@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Rekap Pengajuan Izin/Cuti/Sakit</h5>
                    <div>
                        <a href="{{ route('admin.rekap') }}" class="btn btn-primary btn-sm me-2">
                            <i class="bi bi-calendar-check"></i> Lihat Rekap Absensi
                        </a>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-light btn-sm">
                            <i class="bi bi-arrow-left"></i> Kembali ke Dashboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form action="{{ route('admin.rekap.izin') }}" method="GET" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="tanggal_mulai" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="tanggal_mulai" name="tanggal_mulai" value="{{ $tanggalMulai }}">
                            </div>
                            <div class="col-md-3">
                                <label for="tanggal_akhir" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="{{ $tanggalAkhir }}">
                            </div>
                            <div class="col-md-2">
                                <label for="user_id" class="form-label">Karyawan</label>
                                <select class="form-select" id="user_id" name="user_id">
                                    <option value="">Semua Karyawan</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="jenis" class="form-label">Jenis</label>
                                <select class="form-select" id="jenis" name="jenis">
                                    <option value="">Semua Jenis</option>
                                    <option value="izin" {{ $jenisIzin == 'izin' ? 'selected' : '' }}>Izin</option>
                                    <option value="sakit" {{ $jenisIzin == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                    <option value="cuti" {{ $jenisIzin == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="status_approval" class="form-label">Status</label>
                                <select class="form-select" id="status_approval" name="status_approval">
                                    <option value="">Semua Status</option>
                                    <option value="pending" {{ $statusApproval == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                    <option value="approved" {{ $statusApproval == 'approved' ? 'selected' : '' }}>Disetujui</option>
                                    <option value="rejected" {{ $statusApproval == 'rejected' ? 'selected' : '' }}>Ditolak</option>
                                </select>
                            </div>
                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-filter"></i> Filter
                                </button>
                                <a href="{{ route('admin.rekap.izin') }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-repeat"></i> Reset
                                </a>
                                <a href="{{ route('admin.cetak.rekap.izin.pdf', request()->query()) }}" class="btn btn-danger" target="_blank">
                                    <i class="bi bi-file-pdf"></i> Cetak Semua PDF
                                </a>

                            </div>
                        </div>
                    </form>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="rekapTable">
                            <thead class="table-light">
                                <tr>
                                    <th>No</th>
                                    <th>Karyawan</th>
                                    <th>Jenis</th>
                                    <th>Tanggal</th>
                                    <th>Durasi</th>
                                    <th>Status</th>
                                    <th>Keterangan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pengajuanGrup as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $item->user->name }}</td>
                                        <td>
                                            @if($item->status == 'izin')
                                                <span class="badge bg-warning">Izin</span>
                                            @elseif($item->status == 'sakit')
                                                <span class="badge bg-info">Sakit</span>
                                            @elseif($item->status == 'cuti')
                                                <span class="badge bg-primary">Cuti</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                @if(isset($item->tanggal_mulai) && isset($item->tanggal_selesai))
                                                    <span>{{ \Carbon\Carbon::parse($item->tanggal_mulai)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($item->tanggal_selesai)->format('d/m/Y') }}</span>
                                                @else
                                                    <span>{{ \Carbon\Carbon::parse($item->tanggal_awal ?? $item->tanggal)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($item->tanggal_akhir ?? $item->tanggal)->format('d/m/Y') }}</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>{{ $item->jumlah_hari ?? '1' }} hari</td>
                                        <td>
                                            @if($item->status_approval == 'pending')
                                                <span class="badge bg-secondary">Menunggu</span>
                                            @elseif($item->status_approval == 'approved')
                                                <span class="badge bg-success">Disetujui</span>
                                                @if($item->approval_at)
                                                    <small class="d-block text-muted">{{ \Carbon\Carbon::parse($item->approval_at)->format('d/m/Y H:i') }}</small>
                                                @endif
                                            @elseif($item->status_approval == 'rejected')
                                                <span class="badge bg-danger">Ditolak</span>
                                                @if($item->approval_at)
                                                    <small class="d-block text-muted">{{ \Carbon\Carbon::parse($item->approval_at)->format('d/m/Y H:i') }}</small>
                                                @endif
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ $item->keterangan }}">
                                                {{ $item->keterangan }}
                                            </span>
                                            @if($item->approval_note)
                                                <small class="d-block text-muted">Catatan: {{ $item->approval_note }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('admin.approval.show', $item->id) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i> Detail
                                                </a>
                                                @if($item->status_approval == 'pending')
                                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#approveModal{{ $item->id }}">
                                                        <i class="bi bi-check-circle"></i> Setujui
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ $item->id }}">
                                                        <i class="bi bi-x-circle"></i> Tolak
                                                    </button>
                                                @endif
                                            </div>

                                            <!-- Approve Modal -->
                                            <div class="modal fade" id="approveModal{{ $item->id }}" tabindex="-1" aria-labelledby="approveModalLabel{{ $item->id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <form action="{{ route('admin.approval.approve', $item->id) }}" method="POST">
                                                            @csrf
                                                            <div class="modal-header bg-success text-white">
                                                                <h5 class="modal-title" id="approveModalLabel{{ $item->id }}">Setujui Pengajuan</h5>
                                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p>Anda yakin ingin menyetujui pengajuan ini?</p>
                                                                <div class="mb-3">
                                                                    <label for="approval_note" class="form-label">Catatan (Opsional)</label>
                                                                    <textarea class="form-control" id="approval_note" name="approval_note" rows="3"></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                                <button type="submit" class="btn btn-success">Setujui</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Reject Modal -->
                                            <div class="modal fade" id="rejectModal{{ $item->id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ $item->id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <form action="{{ route('admin.approval.reject', $item->id) }}" method="POST">
                                                            @csrf
                                                            <div class="modal-header bg-danger text-white">
                                                                <h5 class="modal-title" id="rejectModalLabel{{ $item->id }}">Tolak Pengajuan</h5>
                                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p>Anda yakin ingin menolak pengajuan ini?</p>
                                                                <div class="mb-3">
                                                                    <label for="approval_note" class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
                                                                    <textarea class="form-control" id="approval_note" name="approval_note" rows="3" required></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                                <button type="submit" class="btn btn-danger">Tolak</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="bi bi-inbox fs-1 text-muted mb-2"></i>
                                                <h5>Tidak ada data pengajuan</h5>
                                                <p class="text-muted">Belum ada pengajuan izin/cuti/sakit yang tersedia</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // Implementasi export Excel
    alert('Fitur export Excel akan segera tersedia');
}

function exportToPDF() {
    // Fitur export PDF dinonaktifkan sementara
    alert('Fitur export PDF sedang dalam perbaikan dan dinonaktifkan sementara');
}

document.addEventListener('DOMContentLoaded', function() {
    // Aktifkan semua tooltip
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection
