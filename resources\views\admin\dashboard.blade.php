@extends('layouts.app')

@section('styles')
<style>
    /* Gradient Backgrounds */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #36b9cc 0%, #258391 100%) !important;
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%) !important;
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%) !important;
    }

    .bg-gradient-dark {
        background: linear-gradient(135deg, #5a5c69 0%, #373840 100%) !important;
    }

    /* Card Hover Effects */
    .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
    }

    /* Dot indicators */
    .dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
</style>
@endsection

@section('content')
<div class="container-fluid px-4">
    <!-- Welcome Card -->
    <div class="row mb-4 mt-3">
        <div class="col-md-12">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <div class="col-md-8 position-relative">
                            <div class="p-4 p-lg-5" style="background: linear-gradient(120deg, #f8f9fa 0%, #e9ecef 100%);">
                                <div class="d-flex align-items-center mb-4">
                                    <div class="avatar-lg bg-white shadow-sm text-primary rounded-circle p-2 me-4 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-person-circle fs-1"></i>
                                    </div>
                                    <div>
                                        <h3 class="mb-1 fw-bold text-dark">Selamat datang, {{ Auth::user()->name }}!</h3>
                                        <p class="text-muted mb-0">Dashboard Admin Sistem Absensi Pegawai</p>
                                    </div>
                                </div>

                                <div class="row mt-4 mb-2">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="icon-circle bg-primary text-white me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                                                <i class="bi bi-people"></i>
                                            </div>
                                            <div>
                                                <p class="mb-0 small text-muted">Total Pegawai</p>
                                                <h4 class="mb-0 fw-bold">{{ $userCount }} <small class="text-success">Aktif: {{ $userActive }}</small></h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="icon-circle bg-warning text-white me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                                                <i class="bi bi-hourglass-split"></i>
                                            </div>
                                            <div>
                                                <p class="mb-0 small text-muted">Pengajuan Pending</p>
                                                <h4 class="mb-0 fw-bold">{{ $izinPending }} <small class="text-muted">menunggu persetujuan</small></h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex flex-wrap gap-3 mt-4">
                                    <a href="{{ route('admin.approval.index', ['status_approval' => 'pending']) }}" class="btn btn-warning px-4 py-2 rounded-pill shadow-sm d-inline-flex align-items-center">
                                        <i class="bi bi-check-circle me-2"></i> Persetujuan Izin
                                        @if($izinPending > 0)
                                            <span class="badge bg-white text-warning ms-2">{{ $izinPending }}</span>
                                        @endif
                                    </a>
                                    <a href="{{ route('admin.rekap') }}" class="btn btn-info text-white px-4 py-2 rounded-pill shadow-sm">
                                        <i class="bi bi-file-earmark-text me-2"></i> Rekap Absensi
                                    </a>
                                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary px-4 py-2 rounded-pill shadow-sm">
                                        <i class="bi bi-people me-2"></i> Kelola Pegawai
                                    </a>
                                </div>

                                <!-- Decorative elements -->
                                <div class="position-absolute" style="width: 150px; height: 150px; border-radius: 50%; background-color: rgba(0,123,255,0.05); top: -50px; right: -50px; z-index: 0;"></div>
                                <div class="position-absolute" style="width: 100px; height: 100px; border-radius: 50%; background-color: rgba(40,167,69,0.05); bottom: -30px; left: 30%; z-index: 0;"></div>
                            </div>
                        </div>
                        <div class="col-md-4 position-relative overflow-hidden" style="background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);">
                            <div class="position-absolute" style="width: 200px; height: 200px; border-radius: 50%; background-color: rgba(255,255,255,0.1); top: -100px; right: -100px;"></div>
                            <div class="position-absolute" style="width: 150px; height: 150px; border-radius: 50%; background-color: rgba(255,255,255,0.1); bottom: -50px; left: -50px;"></div>

                            <div class="d-flex flex-column justify-content-center text-white p-4 h-100 position-relative">
                                <div class="text-center mb-4">
                                    <div class="display-4 fw-bold" id="clock" style="text-shadow: 0 2px 4px rgba(0,0,0,0.1);">00:00:00</div>
                                    <p class="mb-0 opacity-75">{{ date('l, d F Y') }}</p>
                                </div>
                                <div class="row text-center g-3">
                                    <div class="col-6">
                                        <div class="border border-white border-opacity-25 bg-white bg-opacity-10 rounded-4 p-3 shadow-sm hover-lift">
                                            <h2 class="mb-0 fw-bold">{{ $absensiToday }}</h2>
                                            <small class="opacity-75">Hadir Hari Ini</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="border border-white border-opacity-25 bg-white bg-opacity-10 rounded-4 p-3 shadow-sm hover-lift">
                                            <h2 class="mb-0 fw-bold">{{ $absensiMonth }}</h2>
                                            <small class="opacity-75">Bulan Ini</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Cepat -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header py-3 d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white shadow-sm me-3" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-lightning-charge-fill text-primary fs-4"></i>
                        </div>
                        <h5 class="mb-0 fw-bold text-white">Menu Cepat</h5>
                    </div>
                    <span class="badge bg-white text-primary rounded-pill px-3 py-2 shadow-sm">Administrator</span>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-3 col-sm-6">
                            <a href="{{ route('admin.approval.index', ['status_approval' => 'pending']) }}" class="card text-decoration-none border-0 shadow-sm rounded-4 overflow-hidden hover-lift h-100">
                                <div class="card-body p-0">
                                    <div class="p-4 text-center" style="background: linear-gradient(135deg, #fff8e1 0%, #fffde7 100%);">
                                        <div class="icon-circle mx-auto mb-3 shadow-sm" style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-check-circle text-white fs-3"></i>
                                        </div>
                                        <h5 class="fw-bold mb-0 text-warning">Persetujuan Izin</h5>
                                    </div>
                                    <div class="p-3 text-center">
                                        <p class="text-muted small mb-2">Kelola pengajuan izin, cuti, dan sakit</p>
                                        @if($izinPending > 0)
                                            <span class="badge bg-danger rounded-pill px-3">{{ $izinPending }} menunggu</span>
                                        @else
                                            <span class="badge bg-success rounded-pill px-3">Tidak ada pengajuan</span>
                                        @endif
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{{ route('admin.users.create') }}" class="card text-decoration-none border-0 shadow-sm rounded-4 overflow-hidden hover-lift h-100">
                                <div class="card-body p-0">
                                    <div class="p-4 text-center" style="background: linear-gradient(135deg, #e8f5e9 0%, #f1f8e9 100%);">
                                        <div class="icon-circle mx-auto mb-3 shadow-sm" style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #198754 0%, #20c997 100%); display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-person-plus text-white fs-3"></i>
                                        </div>
                                        <h5 class="fw-bold mb-0 text-success">Tambah Pegawai</h5>
                                    </div>
                                    <div class="p-3 text-center">
                                        <p class="text-muted small mb-0">Daftarkan pegawai baru ke sistem</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{{ route('admin.rekap') }}" class="card text-decoration-none border-0 shadow-sm rounded-4 overflow-hidden hover-lift h-100">
                                <div class="card-body p-0">
                                    <div class="p-4 text-center" style="background: linear-gradient(135deg, #e3f2fd 0%, #e6f7ff 100%);">
                                        <div class="icon-circle mx-auto mb-3 shadow-sm" style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #17a2b8 0%, #0dcaf0 100%); display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-file-earmark-text text-white fs-3"></i>
                                        </div>
                                        <h5 class="fw-bold mb-0 text-info">Rekap Absensi</h5>
                                    </div>
                                    <div class="p-3 text-center">
                                        <p class="text-muted small mb-0">Lihat laporan kehadiran pegawai</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{{ route('admin.settings') }}" class="card text-decoration-none border-0 shadow-sm rounded-4 overflow-hidden hover-lift h-100">
                                <div class="card-body p-0">
                                    <div class="p-4 text-center" style="background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);">
                                        <div class="icon-circle mx-auto mb-3 shadow-sm" style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%); display: flex; align-items: center; justify-content: center;">
                                            <i class="bi bi-gear text-white fs-3"></i>
                                        </div>
                                        <h5 class="fw-bold mb-0 text-secondary">Pengaturan</h5>
                                    </div>
                                    <div class="p-3 text-center">
                                        <p class="text-muted small mb-0">Konfigurasi sistem absensi</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistik Utama -->
    <div class="row">
        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-lg rounded-4 h-100 hover-lift overflow-hidden">
                <div class="card-body p-0">
                    <div class="position-relative">
                        <div class="p-4" style="background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="fw-bold mb-0 text-white">Total Pengguna</h6>
                                <div class="icon-circle bg-white shadow-sm" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-people text-primary"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold text-white mb-0 display-6">{{ $userCount }}</h3>
                            <p class="text-white text-opacity-75 mb-0">Pengguna aktif: {{ $userActive }}</p>

                            <!-- Decorative elements -->
                            <div class="position-absolute" style="width: 100px; height: 100px; border-radius: 50%; background-color: rgba(255,255,255,0.1); top: -50px; right: -50px;"></div>
                            <div class="position-absolute" style="width: 70px; height: 70px; border-radius: 50%; background-color: rgba(255,255,255,0.1); bottom: -20px; left: 20px;"></div>
                        </div>
                        <div class="p-3 text-center bg-white">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-primary rounded-pill px-4 shadow-sm">
                                <i class="bi bi-eye me-1"></i> Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-lg rounded-4 h-100 hover-lift overflow-hidden">
                <div class="card-body p-0">
                    <div class="position-relative">
                        <div class="p-4" style="background: linear-gradient(135deg, #198754 0%, #20c997 100%);">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="fw-bold mb-0 text-white">Absensi Hari Ini</h6>
                                <div class="icon-circle bg-white shadow-sm" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-calendar-check text-success"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold text-white mb-0 display-6">{{ $absensiToday }}</h3>
                            <p class="text-white text-opacity-75 mb-0">Tanggal: {{ date('d M Y') }}</p>

                            <!-- Decorative elements -->
                            <div class="position-absolute" style="width: 100px; height: 100px; border-radius: 50%; background-color: rgba(255,255,255,0.1); top: -50px; right: -50px;"></div>
                            <div class="position-absolute" style="width: 70px; height: 70px; border-radius: 50%; background-color: rgba(255,255,255,0.1); bottom: -20px; left: 20px;"></div>
                        </div>
                        <div class="p-3 text-center bg-white">
                            <a href="{{ route('admin.absensi') }}" class="btn btn-success rounded-pill px-4 shadow-sm">
                                <i class="bi bi-eye me-1"></i> Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-lg rounded-4 h-100 hover-lift overflow-hidden">
                <div class="card-body p-0">
                    <div class="position-relative">
                        <div class="p-4" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="fw-bold mb-0 text-white">Pengajuan Pending</h6>
                                <div class="icon-circle bg-white shadow-sm" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-hourglass-split text-warning"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold text-white mb-0 display-6">{{ $izinPending }}</h3>
                            <p class="text-white text-opacity-75 mb-0">Menunggu persetujuan</p>

                            <!-- Decorative elements -->
                            <div class="position-absolute" style="width: 100px; height: 100px; border-radius: 50%; background-color: rgba(255,255,255,0.1); top: -50px; right: -50px;"></div>
                            <div class="position-absolute" style="width: 70px; height: 70px; border-radius: 50%; background-color: rgba(255,255,255,0.1); bottom: -20px; left: 20px;"></div>
                        </div>
                        <div class="p-3 text-center bg-white">
                            <a href="{{ route('admin.approval.index', ['status_approval' => 'pending']) }}" class="btn btn-warning text-white rounded-pill px-4 shadow-sm">
                                <i class="bi bi-eye me-1"></i> Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-lg rounded-4 h-100 hover-lift overflow-hidden">
                <div class="card-body p-0">
                    <div class="position-relative">
                        <div class="p-4" style="background: linear-gradient(135deg, #17a2b8 0%, #0dcaf0 100%);">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="fw-bold mb-0 text-white">Total Bulan Ini</h6>
                                <div class="icon-circle bg-white shadow-sm" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-calendar-month text-info"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold text-white mb-0 display-6">{{ $absensiMonth }}</h3>
                            <p class="text-white text-opacity-75 mb-0">Bulan: {{ date('F Y') }}</p>

                            <!-- Decorative elements -->
                            <div class="position-absolute" style="width: 100px; height: 100px; border-radius: 50%; background-color: rgba(255,255,255,0.1); top: -50px; right: -50px;"></div>
                            <div class="position-absolute" style="width: 70px; height: 70px; border-radius: 50%; background-color: rgba(255,255,255,0.1); bottom: -20px; left: 20px;"></div>
                        </div>
                        <div class="p-3 text-center bg-white">
                            <a href="{{ route('admin.rekap') }}" class="btn btn-info text-white rounded-pill px-4 shadow-sm">
                                <i class="bi bi-eye me-1"></i> Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Pegawai Hadir -->
    <div class="row">
        <div class="col-lg-8 col-md-7 mb-4">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header bg-gradient-success text-white py-3 d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white text-success me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <h5 class="mb-0 fw-bold">Pegawai Hadir Hari Ini</h5>
                    </div>
                    <a href="{{ route('admin.absensi') }}" class="btn btn-sm btn-success text-white rounded-pill px-3">
                        <i class="bi bi-list me-1"></i> Lihat Semua
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="ps-4">Nama</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Keluar</th>
                                    <th class="pe-4">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pegawaiHadirHariIni as $absen)
                                <tr>
                                    <td class="ps-4">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3 bg-primary text-white rounded-circle">
                                                {{ strtoupper(substr($absen->user->name, 0, 1)) }}
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-semibold">{{ $absen->user->name }}</h6>
                                                <small class="text-muted">{{ $absen->user->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-clock text-success me-2"></i>
                                            @php
                                                try {
                                                    echo $absen->jam_masuk ? \Carbon\Carbon::parse($absen->jam_masuk)->format('H:i:s') : '-';
                                                } catch (\Exception $e) {
                                                    echo $absen->jam_masuk ?? '-';
                                                }
                                            @endphp
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-clock-history text-info me-2"></i>
                                            @php
                                                try {
                                                    echo $absen->jam_keluar ? \Carbon\Carbon::parse($absen->jam_keluar)->format('H:i:s') : '-';
                                                } catch (\Exception $e) {
                                                    echo $absen->jam_keluar ?? '-';
                                                }
                                            @endphp
                                        </div>
                                    </td>
                                    <td class="pe-4">
                                        @php
                                            try {
                                                $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                                $toleransiKeterlambatan = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');
                                                $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                                $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);

                                                $isTerlambat = false;
                                                if ($absen->jam_masuk) {
                                                    $jamMasukUser = \Carbon\Carbon::parse($absen->jam_masuk);
                                                    $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                                }
                                            } catch (\Exception $e) {
                                                $isTerlambat = false;
                                            }
                                        @endphp

                                        @if($isTerlambat)
                                            <span class="badge bg-warning rounded-pill px-3 py-2">
                                                <i class="bi bi-exclamation-triangle me-1"></i> Terlambat
                                            </span>
                                        @else
                                            <span class="badge bg-success rounded-pill px-3 py-2">
                                                <i class="bi bi-check-circle me-1"></i> Tepat Waktu
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <div class="icon-lg bg-light text-secondary rounded-circle mb-3">
                                                <i class="bi bi-calendar-x"></i>
                                            </div>
                                            <h6 class="fw-semibold">Belum ada pegawai yang hadir</h6>
                                            <p class="text-muted small">Data kehadiran hari ini akan muncul di sini</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-5 mb-4">
            <div class="card border-0 shadow-lg rounded-4 h-100 hover-lift overflow-hidden">
                <div class="card-body p-0">
                    <div class="position-relative">
                        <div class="p-4 bg-gradient-primary">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="fw-bold mb-0 text-white">Statistik Pengajuan</h6>
                                <div class="icon-circle bg-white text-primary shadow-sm" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                            </div>
                            <h3 class="fw-bold text-white mb-2">Izin, Sakit & Cuti</h3>
                            <p class="text-white text-opacity-75 mb-0">Total: {{ $izinPending + $izinApproved + $izinRejected }} pengajuan</p>

                            <!-- Decorative elements -->
                            <div class="position-absolute" style="width: 100px; height: 100px; border-radius: 50%; background-color: rgba(255,255,255,0.1); top: -50px; right: -50px;"></div>
                            <div class="position-absolute" style="width: 70px; height: 70px; border-radius: 50%; background-color: rgba(255,255,255,0.1); bottom: -20px; left: 20px;"></div>
                        </div>
                        <div class="p-4 bg-white">
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="badge bg-warning rounded-pill px-3 py-2">
                                            <i class="bi bi-hourglass-split me-1"></i> Menunggu
                                        </span>
                                    </div>
                                    <h5 class="fw-bold mb-0">{{ $izinPending }}</h5>
                                </div>
                                <div class="progress" style="height: 8px; border-radius: 4px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                    <div class="progress-bar bg-warning" role="progressbar"
                                        style="width: {{ ($izinPending + $izinApproved + $izinRejected) > 0 ? ($izinPending / ($izinPending + $izinApproved + $izinRejected) * 100) : 0 }}%"
                                        aria-valuenow="{{ $izinPending }}" aria-valuemin="0" aria-valuemax="{{ $izinPending + $izinApproved + $izinRejected }}"></div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="badge bg-success rounded-pill px-3 py-2">
                                            <i class="bi bi-check-circle me-1"></i> Disetujui
                                        </span>
                                    </div>
                                    <h5 class="fw-bold mb-0">{{ $izinApproved }}</h5>
                                </div>
                                <div class="progress" style="height: 8px; border-radius: 4px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                    <div class="progress-bar bg-success" role="progressbar"
                                        style="width: {{ ($izinPending + $izinApproved + $izinRejected) > 0 ? ($izinApproved / ($izinPending + $izinApproved + $izinRejected) * 100) : 0 }}%"
                                        aria-valuenow="{{ $izinApproved }}" aria-valuemin="0" aria-valuemax="{{ $izinPending + $izinApproved + $izinRejected }}"></div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="badge bg-danger rounded-pill px-3 py-2">
                                            <i class="bi bi-x-circle me-1"></i> Ditolak
                                        </span>
                                    </div>
                                    <h5 class="fw-bold mb-0">{{ $izinRejected }}</h5>
                                </div>
                                <div class="progress" style="height: 8px; border-radius: 4px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                    <div class="progress-bar bg-danger" role="progressbar"
                                        style="width: {{ ($izinPending + $izinApproved + $izinRejected) > 0 ? ($izinRejected / ($izinPending + $izinApproved + $izinRejected) * 100) : 0 }}%"
                                        aria-valuenow="{{ $izinRejected }}" aria-valuemin="0" aria-valuemax="{{ $izinPending + $izinApproved + $izinRejected }}"></div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <a href="{{ route('admin.approval.index') }}" class="btn btn-warning text-white rounded-pill px-4 py-2 shadow-sm hover-lift" onclick="showLoading()">
                                    <i class="bi bi-list-ul me-1"></i> Lihat Semua Pengajuan
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widget Alpha Detection -->
    @if(isset($usersWithoutAttendanceToday) && $usersWithoutAttendanceToday > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="row g-0">
                    <div class="col-md-8">
                        <div class="p-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-circle bg-danger text-white me-3" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-exclamation-triangle-fill fs-4"></i>
                                </div>
                                <div>
                                    <h5 class="fw-bold mb-1 text-danger">🚨 DETEKSI ALPHA - {{ $usersWithoutAttendanceToday }} Karyawan Belum Absen</h5>
                                    <p class="mb-0 text-muted">Hari ini {{ date('d/m/Y') }} - {{ date('l') }}</p>
                                </div>
                            </div>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="bg-light p-3 rounded-3">
                                        <h6 class="fw-bold mb-1">Alpha Hari Ini</h6>
                                        <h4 class="text-danger mb-0">{{ $alphaToday ?? 0 }}</h4>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="bg-light p-3 rounded-3">
                                        <h6 class="fw-bold mb-1">Alpha Kemarin</h6>
                                        <h4 class="text-warning mb-0">{{ $alphaYesterday ?? 0 }}</h4>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="bg-light p-3 rounded-3">
                                        <h6 class="fw-bold mb-1">Alpha Bulan Ini</h6>
                                        <h4 class="text-info mb-0">{{ $alphaBulanIni ?? 0 }}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 bg-danger text-white p-4 d-flex flex-column justify-content-center">
                        <div class="text-center">
                            <i class="bi bi-robot display-4 mb-3"></i>
                            <h6 class="fw-bold mb-3">Tindakan Otomatis</h6>
                            <a href="{{ route('admin.attendance.index') }}" class="btn btn-light btn-sm rounded-pill px-4 mb-2">
                                <i class="bi bi-eye me-1"></i> Lihat Detail
                            </a>
                            <br>
                            <small class="opacity-75">Kelola alpha & potongan gaji</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Statistik Kehadiran dan Izin -->
    <div class="row">
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <div class="col-md-5 bg-gradient-success text-white p-4">
                            <h5 class="fw-bold mb-4">Statistik Kehadiran</h5>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="text-center">
                                    <h2 class="display-4 fw-bold mb-0">{{ $tepatWaktuBulanIni }}</h2>
                                    <p class="mb-0 opacity-75">Tepat Waktu</p>
                                </div>
                                <div class="text-center">
                                    <h2 class="display-4 fw-bold mb-0">{{ $terlambatBulanIni }}</h2>
                                    <p class="mb-0 opacity-75">Terlambat</p>
                                </div>
                            </div>
                            <div class="mt-4 text-center">
                                <span class="badge bg-white text-success px-3 py-2 rounded-pill">{{ date('F Y') }}</span>
                            </div>
                        </div>
                        <div class="col-md-7 p-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="fw-bold mb-0">Persentase Kehadiran</h6>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-success rounded-pill">{{ $tepatWaktuBulanIni + $terlambatBulanIni }} Total</span>
                                </div>
                            </div>
                            <div class="chart-container position-relative" style="height: 220px;">
                                <canvas id="kehadiranChart"></canvas>
                            </div>
                            <div class="d-flex justify-content-around mt-3">
                                <div class="text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="dot bg-success me-2"></div>
                                        <span class="small">Tepat Waktu</span>
                                    </div>
                                    <h5 class="fw-bold mt-1 mb-0">
                                        @php
                                            $total = $tepatWaktuBulanIni + $terlambatBulanIni;
                                            $persenTepat = $total > 0 ? round(($tepatWaktuBulanIni / $total) * 100) : 0;
                                        @endphp
                                        {{ $persenTepat }}%
                                    </h5>
                                </div>
                                <div class="text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="dot bg-warning me-2"></div>
                                        <span class="small">Terlambat</span>
                                    </div>
                                    <h5 class="fw-bold mt-1 mb-0">
                                        @php
                                            $persenTerlambat = $total > 0 ? round(($terlambatBulanIni / $total) * 100) : 0;
                                        @endphp
                                        {{ $persenTerlambat }}%
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <div class="col-md-7 p-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="fw-bold mb-0">Distribusi Pengajuan</h6>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-primary rounded-pill">{{ $izinBulanIni + $sakitBulanIni + $cutiBulanIni }} Total</span>
                                </div>
                            </div>
                            <div class="chart-container position-relative" style="height: 220px;">
                                <canvas id="izinChart"></canvas>
                            </div>
                            <div class="d-flex justify-content-around mt-3">
                                <div class="text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="dot bg-warning me-2"></div>
                                        <span class="small">Izin</span>
                                    </div>
                                    <h5 class="fw-bold mt-1 mb-0">{{ $izinBulanIni }}</h5>
                                </div>
                                <div class="text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="dot bg-info me-2"></div>
                                        <span class="small">Sakit</span>
                                    </div>
                                    <h5 class="fw-bold mt-1 mb-0">{{ $sakitBulanIni }}</h5>
                                </div>
                                <div class="text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="dot bg-primary me-2"></div>
                                        <span class="small">Cuti</span>
                                    </div>
                                    <h5 class="fw-bold mt-1 mb-0">{{ $cutiBulanIni }}</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5 bg-gradient-primary text-white p-4">
                            <h5 class="fw-bold mb-4">Statistik Pengajuan</h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <div class="p-3 bg-white bg-opacity-10 rounded-4 hover-lift">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <p class="mb-0 small opacity-75">Izin</p>
                                                <h4 class="fw-bold mb-0">{{ $izinBulanIni }}</h4>
                                            </div>
                                            <div class="icon-circle bg-warning text-white">
                                                <i class="bi bi-calendar-x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="p-3 bg-white bg-opacity-10 rounded-4 hover-lift">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <p class="mb-0 small opacity-75">Sakit</p>
                                                <h4 class="fw-bold mb-0">{{ $sakitBulanIni }}</h4>
                                            </div>
                                            <div class="icon-circle bg-info text-white">
                                                <i class="bi bi-thermometer"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="p-3 bg-white bg-opacity-10 rounded-4 hover-lift">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <p class="mb-0 small opacity-75">Cuti</p>
                                                <h4 class="fw-bold mb-0">{{ $cutiBulanIni }}</h4>
                                            </div>
                                            <div class="icon-circle bg-primary text-white" style="background-color: rgba(255,255,255,0.2) !important;">
                                                <i class="bi bi-calendar-check"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistik Mingguan dan Kalender -->
    <div class="row">
        <div class="col-lg-7 col-md-7 mb-4">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header bg-gradient-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white text-primary me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <h5 class="mb-0 fw-bold">Statistik Mingguan</h5>
                    </div>
                    <div class="badge bg-white text-primary rounded-pill px-3 py-2">7 Hari Terakhir</div>
                </div>
                <div class="card-body p-4">
                    <canvas id="weeklyChart" height="280"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-5 col-md-5 mb-4">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header p-0">
                    <div class="calendar-header p-3 text-white bg-gradient-primary">
                        <div class="calendar-header-content">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="icon-circle bg-white text-primary me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                                        <i class="bi bi-calendar-week"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0 fw-bold">Kalender Kehadiran</h5>
                                        <p class="mb-0 small text-white-50">Statistik kehadiran pegawai</p>
                                    </div>
                                </div>
                                <div class="badge bg-white text-primary rounded-pill px-3 py-2 d-flex align-items-center">
                                    <i class="bi bi-calendar-date me-2"></i>
                                    <span class="fw-bold">{{ date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="position-absolute" style="width: 150px; height: 150px; border-radius: 50%; background-color: rgba(255,255,255,0.1); top: -75px; right: -75px;"></div>
                        <div class="position-absolute" style="width: 100px; height: 100px; border-radius: 50%; background-color: rgba(255,255,255,0.1); bottom: -50px; left: 10%;"></div>
                    </div>

                    <div class="calendar-filter-container mx-3 mt-3 mb-2">
                        <div class="calendar-filter-card">
                            <form id="calendarForm" action="{{ route('admin.dashboard') }}" method="GET">
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <label class="form-label small fw-bold text-muted mb-1">Bulan</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">
                                                <i class="bi bi-calendar-month"></i>
                                            </span>
                                            <select name="month" class="form-select" onchange="document.getElementById('calendarForm').submit()">
                                                @for ($i = 1; $i <= 12; $i++)
                                                    <option value="{{ $i }}" {{ $currentMonth == $i ? 'selected' : '' }}>
                                                        {{ date('F', mktime(0, 0, 0, $i, 1)) }}
                                                    </option>
                                                @endfor
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label small fw-bold text-muted mb-1">Tahun</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">
                                                <i class="bi bi-calendar-event"></i>
                                            </span>
                                            <select name="year" class="form-select" onchange="document.getElementById('calendarForm').submit()">
                                                @for ($i = $currentYear - 2; $i <= $currentYear + 2; $i++)
                                                    <option value="{{ $i }}" {{ $currentYear == $i ? 'selected' : '' }}>
                                                        {{ $i }}
                                                    </option>
                                                @endfor
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div id="calendar" class="calendar-container">
                        <table class="table table-bordered calendar-table mb-2">
                            <thead>
                                <tr class="text-center">
                                    <th class="text-danger">Min</th>
                                    <th>Sen</th>
                                    <th>Sel</th>
                                    <th>Rab</th>
                                    <th>Kam</th>
                                    <th>Jum</th>
                                    <th class="text-danger">Sab</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    try {
                                        $startDate = \Carbon\Carbon::createFromDate($currentYear, $currentMonth, 1)->startOfMonth();
                                        $endDate = \Carbon\Carbon::createFromDate($currentYear, $currentMonth, 1)->endOfMonth();

                                        // Mendapatkan hari pertama bulan ini (0 = Minggu, 1 = Senin, dst)
                                        $firstDayOfWeek = $startDate->dayOfWeek;

                                        // Jumlah hari dalam bulan ini
                                        $daysInMonth = $endDate->day;

                                        // Tanggal hari ini (hanya jika bulan dan tahun sama dengan sekarang)
                                        $today = (\Carbon\Carbon::now()->year == $currentYear && \Carbon\Carbon::now()->month == $currentMonth)
                                                ? \Carbon\Carbon::now()->day
                                                : null;

                                        // Inisialisasi array untuk menyimpan tanggal
                                        $calendar = [];
                                        $day = 1;

                                        // Mengisi array calendar
                                        for ($i = 0; $i < 6; $i++) {
                                            $week = [];
                                            for ($j = 0; $j < 7; $j++) {
                                                if (($i == 0 && $j < $firstDayOfWeek) || ($day > $daysInMonth)) {
                                                    $week[] = null;
                                                } else {
                                                    $week[] = $day;
                                                    $day++;
                                                }
                                            }
                                            $calendar[] = $week;
                                            if ($day > $daysInMonth) {
                                                break;
                                            }
                                        }
                                    } catch (\Exception $e) {
                                        $calendar = [];
                                    }
                                @endphp

                                @foreach($calendar as $week)
                                    <tr>
                                        @foreach($week as $day)
                                            @if($day === null)
                                                <td class="empty-day"></td>
                                            @else
                                                @php
                                                    try {
                                                        $date = \Carbon\Carbon::create($currentYear, $currentMonth, $day)->format('Y-m-d');
                                                        $isToday = ($today !== null && $day == $today);
                                                        $hasData = isset($calendarData[$date]);
                                                        $hadir = $hasData ? $calendarData[$date]['hadir'] : 0;
                                                        $izin = $hasData ? $calendarData[$date]['izin'] : 0;
                                                        $sakit = $hasData ? $calendarData[$date]['sakit'] : 0;
                                                        $cuti = $hasData ? $calendarData[$date]['cuti'] : 0;
                                                        $alpha = $hasData ? ($calendarData[$date]['alpha'] ?? 0) : 0;
                                                        $total = $hasData ? $calendarData[$date]['total'] : 0;

                                                        $bgClass = '';
                                                        $borderClass = '';

                                                        if ($isToday) {
                                                            $bgClass = 'bg-primary bg-opacity-10';
                                                            $borderClass = 'border-primary';
                                                        }
                                                    } catch (\Exception $e) {
                                                        $hadir = 0;
                                                        $izin = 0;
                                                        $sakit = 0;
                                                        $cuti = 0;
                                                        $total = 0;
                                                        $bgClass = '';
                                                        $borderClass = '';
                                                    }
                                                @endphp

                                                <td class="calendar-day {{ $isToday ? 'today' : '' }} {{ $total > 0 ? 'has-events' : '' }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Hadir: {{ $hadir }}, Alpha: {{ $alpha }}, Izin: {{ $izin }}, Sakit: {{ $sakit }}, Cuti: {{ $cuti }}">
                                                    <div class="day-number {{ $isToday ? 'today-number' : '' }}">{{ $day }}</div>
                                                    @if($total > 0)
                                                        <div class="day-indicator">
                                                            @if($hadir > 0)
                                                                <span class="badge bg-success rounded-pill" title="Hadir">{{ $hadir }}</span>
                                                            @endif
                                                            @if($alpha > 0)
                                                                <span class="badge bg-danger rounded-pill" title="Alpha">{{ $alpha }}</span>
                                                            @endif
                                                            @if($izin + $sakit + $cuti > 0)
                                                                <span class="badge bg-warning rounded-pill" title="Izin/Sakit/Cuti">{{ $izin + $sakit + $cuti }}</span>
                                                            @endif
                                                        </div>
                                                    @endif
                                                </td>
                                            @endif
                                        @endforeach
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="calendar-legend-card mt-3">
                            <div class="calendar-legend-header">
                                <i class="bi bi-info-circle me-2"></i>
                                <span>Keterangan Kalender</span>
                            </div>
                            <div class="calendar-legend-body">
                                <div class="d-flex justify-content-center gap-4">
                                    <div class="legend-item">
                                        <span class="legend-dot bg-success"></span>
                                        <span class="legend-text">Hadir</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-dot bg-warning"></span>
                                        <span class="legend-text">Izin/Sakit/Cuti</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-dot bg-primary"></span>
                                        <span class="legend-text">Hari Ini</span>
                                    </div>
                                </div>
                                <div class="calendar-legend-tip">
                                    <i class="bi bi-cursor-fill me-1 text-primary"></i>
                                    <span>Klik pada tanggal untuk melihat detail kehadiran</span>
                                </div>
                            </div>
                        </div>

                        <div class="calendar-navigation mt-4">
                            @php
                                $prevMonth = $currentMonth - 1;
                                $prevYear = $currentYear;
                                if ($prevMonth < 1) {
                                    $prevMonth = 12;
                                    $prevYear--;
                                }

                                $nextMonth = $currentMonth + 1;
                                $nextYear = $currentYear;
                                if ($nextMonth > 12) {
                                    $nextMonth = 1;
                                    $nextYear++;
                                }

                                $prevMonthName = date('F', mktime(0, 0, 0, $prevMonth, 1));
                                $nextMonthName = date('F', mktime(0, 0, 0, $nextMonth, 1));
                                $isCurrentMonth = $currentMonth == date('m') && $currentYear == date('Y');
                            @endphp
                            <div class="calendar-nav-simple">
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="{{ route('admin.dashboard', ['month' => $prevMonth, 'year' => $prevYear]) }}" class="btn btn-light calendar-nav-arrow" data-bs-toggle="tooltip" data-bs-placement="top" title="Bulan Sebelumnya">
                                        <i class="bi bi-chevron-left"></i>
                                    </a>

                                    <a href="{{ route('admin.dashboard') }}" class="calendar-current-month {{ $isCurrentMonth ? 'is-current' : '' }}">
                                        <span class="month-name">{{ date('F', mktime(0, 0, 0, $currentMonth, 1)) }}</span>
                                        <span class="year-name">{{ $currentYear }}</span>
                                        @if($isCurrentMonth)
                                            <span class="current-badge">Bulan Ini</span>
                                        @endif
                                    </a>

                                    <a href="{{ route('admin.dashboard', ['month' => $nextMonth, 'year' => $nextYear]) }}" class="btn btn-light calendar-nav-arrow" data-bs-toggle="tooltip" data-bs-placement="top" title="Bulan Berikutnya">
                                        <i class="bi bi-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>






<style>
    /* Icon Styles */
    .icon-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .icon-circle:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .icon-lg {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .icon-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .avatar-sm {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    /* Hover Effects */
    .hover-card {
        transition: all 0.3s ease;
    }

    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .hover-lift {
        transition: all 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;
    }

    /* Decoration Shape */
    .decoration-shape {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        bottom: -50px;
        right: -50px;
        z-index: 0;
    }

    /* Calendar Styles */
    .calendar-container {
        position: relative;
        z-index: 1;
    }

    .calendar-table {
        table-layout: fixed;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: none;
        margin-bottom: 0;
        background-color: #fff;
        transform: translateY(0);
        transition: transform 0.3s ease;
    }

    .calendar-table:hover {
        transform: translateY(-5px);
    }

    .input-group .form-select {
        border-radius: 0.25rem;
        border: 1px solid #dee2e6;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        font-weight: 500;
        color: #495057;
    }

    .input-group .form-select:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: none;
    }

    .input-group .form-select:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .calendar-table th {
        text-align: center;
        font-size: 0.75rem;
        font-weight: 700;
        padding: 15px 5px;
        background: linear-gradient(135deg, #4e73df, #224abe);
        border-bottom: none;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #fff;
        position: relative;
    }

    .calendar-table th:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .calendar-table th.text-danger {
        color: #fff !important;
        background: linear-gradient(135deg, #dc3545, #b02a37);
    }

    .calendar-day {
        height: 80px;
        padding: 8px;
        text-align: center;
        vertical-align: top;
        position: relative;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        border: 1px solid #e9ecef;
        cursor: pointer;
        overflow: hidden;
    }

    .calendar-day:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(to right, transparent, transparent);
        transition: all 0.3s ease;
    }

    .calendar-day:hover {
        background-color: #f8f9fa;
        transform: translateY(-3px) scale(1.02);
        z-index: 3;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        border-color: #dee2e6;
    }

    .calendar-day:hover:before {
        background: linear-gradient(to right, #4e73df, #224abe);
    }

    .calendar-day.today {
        background: linear-gradient(135deg, rgba(13, 110, 253, 0.05), rgba(13, 110, 253, 0.1));
        border-color: #0d6efd;
        border-width: 2px;
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.1);
    }

    .calendar-day.today:before {
        background: linear-gradient(to right, #0d6efd, #0a58ca);
    }

    .calendar-day.has-events:after {
        content: '';
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #0d6efd;
        box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.2);
    }

    .empty-day {
        background-color: #f8f9fa;
        border-color: #f8f9fa;
        box-shadow: none;
        cursor: default;
        opacity: 0.7;
    }

    .empty-day:hover {
        transform: none;
        box-shadow: none;
        background-color: #f8f9fa;
    }

    .empty-day:before {
        display: none;
    }

    .day-number {
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 8px;
        position: relative;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .calendar-day:hover .day-number {
        transform: translateY(-2px);
    }

    .day-number.today-number {
        background-color: #0d6efd;
        color: #fff;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        box-shadow: 0 4px 10px rgba(13, 110, 253, 0.3);
        animation: pulse-blue 2s infinite;
    }

    @keyframes pulse-blue {
        0% {
            box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.5);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
        }
    }

    .day-indicator {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 4px;
        transition: all 0.3s ease;
    }

    .calendar-day:hover .day-indicator {
        transform: translateY(-2px);
    }

    .day-indicator .badge {
        font-size: 0.7rem;
        padding: 3px 6px;
        border-radius: 20px;
        font-weight: 600;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        min-width: 24px;
        transition: all 0.3s ease;
    }

    .calendar-day:hover .day-indicator .badge {
        transform: scale(1.1);
    }

    .day-indicator .badge.bg-success {
        background: linear-gradient(45deg, #198754, #20c997) !important;
    }

    .day-indicator .badge.bg-warning {
        background: linear-gradient(45deg, #ffc107, #fd7e14) !important;
    }

    .calendar-legend-card {
        background-color: #ffffff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        margin-top: 20px;
        transition: all 0.3s ease;
    }

    .calendar-legend-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .calendar-legend-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 10px 15px;
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        border-bottom: 1px solid #e9ecef;
    }

    .calendar-legend-body {
        padding: 15px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }

    .legend-item:hover {
        transform: translateY(-2px);
    }

    .legend-dot {
        display: inline-block;
        width: 14px;
        height: 14px;
        margin-right: 8px;
        border-radius: 50%;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .legend-dot.bg-success {
        background: linear-gradient(45deg, #198754, #20c997) !important;
    }

    .legend-dot.bg-warning {
        background: linear-gradient(45deg, #ffc107, #fd7e14) !important;
    }

    .legend-dot.bg-primary {
        background: linear-gradient(45deg, #0d6efd, #0a58ca) !important;
    }

    .legend-text {
        font-weight: 500;
        font-size: 0.85rem;
        color: #495057;
    }

    .calendar-legend-tip {
        margin-top: 12px;
        text-align: center;
        padding: 8px;
        background-color: #f8f9fa;
        border-radius: 6px;
        font-size: 0.8rem;
        color: #6c757d;
        border: 1px dashed #dee2e6;
    }

    /* Calendar Navigation */
    .calendar-navigation {
        border-top: 1px solid #e9ecef;
        padding-top: 15px;
    }

    .calendar-nav-simple {
        background-color: #ffffff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        padding: 15px;
        transition: all 0.3s ease;
    }

    .calendar-nav-simple:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .calendar-nav-arrow {
        width: 45px;
        height: 45px;
        border-radius: 50% !important;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    }

    .calendar-nav-arrow:hover {
        background-color: #4e73df;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
    }

    .calendar-current-month {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #495057;
        position: relative;
        padding: 8px 20px;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .calendar-current-month:hover {
        background-color: #f8f9fa;
        transform: translateY(-3px);
        color: #495057;
    }

    .calendar-current-month.is-current {
        background-color: rgba(13, 110, 253, 0.1);
    }

    .month-name {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 2px;
        color: #4e73df;
    }

    .year-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: #6c757d;
    }

    .current-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #20c997;
        color: white;
        font-size: 0.7rem;
        padding: 3px 8px;
        border-radius: 20px;
        font-weight: 600;
        box-shadow: 0 2px 5px rgba(32, 201, 151, 0.3);
    }

    /* Calendar Header */
    .calendar-header {
        position: relative;
        border-radius: 8px 8px 0 0;
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        box-shadow: 0 4px 20px rgba(13, 110, 253, 0.3);
        overflow: hidden;
        z-index: 1;
    }

    .calendar-header-content {
        position: relative;
        z-index: 2;
    }

    .calendar-header-shape-1 {
        position: absolute;
        top: -20px;
        right: -20px;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        z-index: 1;
    }

    .calendar-header-shape-2 {
        position: absolute;
        bottom: -30px;
        left: 30px;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        z-index: 1;
    }

    .calendar-icon-circle {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
    }

    .calendar-date-badge {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        font-size: 0.9rem;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
    }

    /* Calendar Filter */
    .calendar-filter-container {
        position: relative;
        z-index: 2;
    }

    .calendar-filter-card {
        background-color: #ffffff;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        position: relative;
        top: -10px;
    }

    .calendar-filter-card .form-label {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .calendar-filter-card .form-select {
        border: 1px solid #dee2e6;
        font-weight: 500;
        color: #495057;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .calendar-filter-card .form-select:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .calendar-filter-card .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #6c757d;
    }

    .calendar-filter-card .btn {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        font-weight: 500;
        height: 100%;
        transition: all 0.3s ease;
    }

    .calendar-filter-card .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* Rounded Corners */
    .rounded-4 {
        border-radius: 0.75rem !important;
    }

    /* Animation for Clock */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    #clock {
        animation: pulse 2s infinite;
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Table Hover Effect */
    .table-hover tbody tr {
        transition: all 0.2s ease;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }
</style>

<style>
    /* Dot Indicator */
    .dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
    }

    /* Card Gradient Backgrounds */
    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    }

    /* Rounded Card */
    .rounded-4 {
        border-radius: 0.75rem !important;
    }

    /* Avatar Styles */
    .avatar-lg {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
    }

    /* Icon Styles */
    .icon-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .icon-circle:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .icon-lg {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .icon-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    /* Hover Effects */
    .hover-lift {
        transition: all 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15) !important;
    }

    /* Animation for Clock */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    #clock {
        animation: pulse 2s infinite;
    }

    /* Card Animations */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover .icon-lg,
    .card:hover .icon-circle {
        transform: scale(1.1);
    }

    /* Button Hover Effects */
    .btn {
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Gradient Text */
    .text-gradient {
        background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Tab Navigation Styles */
    .nav-pills .nav-link {
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .nav-pills .nav-link.active {
        background-color: #0d6efd;
        box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
    }

    .nav-pills .nav-link:not(.active):hover {
        background-color: rgba(13, 110, 253, 0.1);
    }

    .nav-sm .nav-link {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update clock
        function updateClock() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            document.getElementById('clock').textContent = `${hours}:${minutes}:${seconds}`;
        }

        setInterval(updateClock, 1000);
        updateClock();

        // Kehadiran Chart
        const kehadiranCtx = document.getElementById('kehadiranChart').getContext('2d');
        const kehadiranChart = new Chart(kehadiranCtx, {
            type: 'doughnut',
            data: {
                labels: ['Tepat Waktu', 'Terlambat'],
                datasets: [{
                    data: [{{ $tepatWaktuBulanIni }}, {{ $terlambatBulanIni }}],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(255, 193, 7, 0.8)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 5,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.formattedValue;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((context.raw / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Izin Chart
        const izinCtx = document.getElementById('izinChart').getContext('2d');
        const izinChart = new Chart(izinCtx, {
            type: 'doughnut',
            data: {
                labels: ['Izin', 'Sakit', 'Cuti'],
                datasets: [{
                    data: [{{ $izinBulanIni }}, {{ $sakitBulanIni }}, {{ $cutiBulanIni }}],
                    backgroundColor: [
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(23, 162, 184, 0.8)',
                        'rgba(0, 123, 255, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 193, 7, 1)',
                        'rgba(23, 162, 184, 1)',
                        'rgba(0, 123, 255, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 5,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.formattedValue;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((context.raw / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Weekly Chart
        const weeklyCtx = document.getElementById('weeklyChart').getContext('2d');
        const weeklyChart = new Chart(weeklyCtx, {
            type: 'bar',
            data: {
                labels: [
                    @foreach($weeklyStats as $stat)
                        '{{ $stat['day'] }} {{ $stat['date'] }}',
                    @endforeach
                ],
                datasets: [
                    {
                        label: 'Hadir',
                        data: [
                            @foreach($weeklyStats as $stat)
                                {{ $stat['hadir'] }},
                            @endforeach
                        ],
                        backgroundColor: 'rgba(40, 167, 69, 0.8)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1,
                        borderRadius: 6,
                        barPercentage: 0.6,
                        categoryPercentage: 0.7
                    },
                    {
                        label: 'Izin/Sakit/Cuti',
                        data: [
                            @foreach($weeklyStats as $stat)
                                {{ $stat['izin'] }},
                            @endforeach
                        ],
                        backgroundColor: 'rgba(255, 193, 7, 0.8)',
                        borderColor: 'rgba(255, 193, 7, 1)',
                        borderWidth: 1,
                        borderRadius: 6,
                        barPercentage: 0.6,
                        categoryPercentage: 0.7
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        },
                        grid: {
                            display: true,
                            drawBorder: false,
                            color: 'rgba(200, 200, 200, 0.15)'
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Kehadiran 7 Hari Terakhir',
                        font: {
                            size: 14,
                            weight: 'bold'
                        },
                        padding: {
                            bottom: 15
                        }
                    }
                }
            }
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });

    // Fungsi untuk menampilkan loading
    function showLoading() {
        // Tambahkan overlay loading
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loadingOverlay';
        loadingOverlay.style.position = 'fixed';
        loadingOverlay.style.top = '0';
        loadingOverlay.style.left = '0';
        loadingOverlay.style.width = '100%';
        loadingOverlay.style.height = '100%';
        loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.justifyContent = 'center';
        loadingOverlay.style.alignItems = 'center';
        loadingOverlay.style.zIndex = '9999';

        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-light';
        spinner.style.width = '3rem';
        spinner.style.height = '3rem';
        spinner.setAttribute('role', 'status');

        const spinnerText = document.createElement('span');
        spinnerText.className = 'visually-hidden';
        spinnerText.textContent = 'Loading...';

        spinner.appendChild(spinnerText);
        loadingOverlay.appendChild(spinner);
        document.body.appendChild(loadingOverlay);

        // Hapus overlay setelah halaman selesai loading
        window.addEventListener('load', function() {
            document.body.removeChild(loadingOverlay);
        });
    }
</script>
@endsection
