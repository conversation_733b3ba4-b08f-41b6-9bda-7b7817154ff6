@extends('layouts.admin')

@section('title', 'Detail Pengajuan Absen Manual')

@section('content')
<style>
    .card-header {
        background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e40af 100%) !important;
        border: none;
        position: relative;
        overflow: hidden;
        min-height: 120px;
    }

    .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(29, 78, 216, 0.15) 0%, transparent 50%);
        opacity: 1;
    }

    .header-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(37, 99, 235, 0.3) 100%);
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        backdrop-filter: blur(20px);
        border: 2px solid rgba(59, 130, 246, 0.6);
        box-shadow:
            0 8px 32px rgba(15, 23, 42, 0.3),
            inset 0 1px 0 rgba(59, 130, 246, 0.4);
    }

    .detail-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 1rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }

    .detail-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.12);
    }

    .action-card {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border: 1px solid #4b5563;
        border-radius: 1rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        color: white;
    }

    .info-item {
        background: rgba(59, 130, 246, 0.05);
        border: 1px solid rgba(59, 130, 246, 0.1);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .info-item:hover {
        background: rgba(59, 130, 246, 0.08);
        border-color: rgba(59, 130, 246, 0.2);
    }

    .info-label {
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        color: #64748b;
        margin-bottom: 0.5rem;
    }

    .info-value {
        font-size: 1rem;
        font-weight: 600;
        color: #1e293b;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.875rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .employee-avatar {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        font-weight: bold;
        color: white;
        border: 3px solid rgba(59, 130, 246, 0.2);
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .reason-box {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 2px solid #cbd5e1;
        border-radius: 15px;
        padding: 1.5rem;
        font-style: italic;
        color: #475569;
        position: relative;
    }

    .reason-box::before {
        content: '"';
        position: absolute;
        top: -10px;
        left: 15px;
        font-size: 3rem;
        color: #cbd5e1;
        font-family: serif;
    }

    .btn-action {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-action:hover::before {
        left: 100%;
    }

    .btn-approve {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }

    .btn-approve:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
        color: white;
    }

    .btn-reject {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
    }

    .btn-reject:hover {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5);
        color: white;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-header text-white py-4 position-relative">
                    <div class="header-bg"></div>
                    <div class="container-fluid position-relative">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="header-icon me-4">
                                        <i class="bi bi-file-text"></i>
                                    </div>
                                    <div>
                                        <h3 class="mb-1 fw-bold text-white">Detail Pengajuan Absen Manual</h3>
                                        <div class="d-flex align-items-center text-white-50">
                                            <i class="bi bi-person me-2"></i>
                                            <span class="fw-medium">{{ $request->user->name }}</span>
                                            <span class="mx-2">•</span>
                                            <i class="bi bi-calendar3 me-2"></i>
                                            <span class="fw-medium">{{ $request->tanggal->format('d F Y') }}</span>
                                        </div>
                                        <div class="d-flex align-items-center text-white-50 mt-1">
                                            <i class="bi bi-clock me-2"></i>
                                            <span class="small">Diajukan: {{ $request->created_at->format('d F Y, H:i') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex gap-2 justify-content-end align-items-center">
                                    @if($request->status == 'pending')
                                        <span class="status-badge" style="background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); color: white;">
                                            <i class="bi bi-clock"></i>{{ $request->status_text }}
                                        </span>
                                    @elseif($request->status == 'approved')
                                        <span class="status-badge" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
                                            <i class="bi bi-check-circle"></i>{{ $request->status_text }}
                                        </span>
                                    @else
                                        <span class="status-badge" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;">
                                            <i class="bi bi-x-circle"></i>{{ $request->status_text }}
                                        </span>
                                    @endif
                                    <a href="{{ route('admin.manual.attendance.requests') }}" class="btn btn-outline-light">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        <span>Kembali</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">

                    <!-- Alert Messages -->
                    @if(session('success'))
                        <div class="alert alert-success border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #10b981 0%, #34d399 100%); color: white;">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check-circle-fill" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">✅ Berhasil</h6>
                                    <p class="mb-0">{{ session('success') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #ef4444 0%, #f87171 100%); color: white;">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-exclamation-triangle-fill" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">❌ Error</h6>
                                    <p class="mb-0">{{ session('error') }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #ef4444 0%, #f87171 100%); color: white;">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-exclamation-triangle-fill" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading mb-1 fw-bold">❌ Validation Error</h6>
                                    <ul class="mb-0">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <!-- Detail Pengajuan -->
                        <div class="col-lg-8">
                            <div class="detail-card mb-4 p-4">
                                <div class="d-flex align-items-center mb-4">
                                    <i class="bi bi-info-circle-fill text-primary me-3" style="font-size: 1.5rem;"></i>
                                    <h5 class="mb-0 fw-bold text-dark">Informasi Pengajuan</h5>
                                </div>

                                <!-- Employee Info -->
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-person me-1"></i>Karyawan
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <div class="employee-avatar me-3">
                                            {{ strtoupper(substr($request->user->name, 0, 2)) }}
                                        </div>
                                        <div>
                                            <div class="info-value">{{ $request->user->name }}</div>
                                            <small class="text-muted">{{ $request->user->jabatan ?? 'Karyawan' }} • {{ $request->user->email }}</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <div class="info-label">
                                                <i class="bi bi-calendar3 me-1"></i>Tanggal Absen
                                            </div>
                                            <div class="info-value">{{ $request->tanggal->format('d F Y') }}</div>
                                            <small class="text-muted">{{ $request->tanggal->format('l') }}</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <div class="info-label">
                                                <i class="bi bi-clock me-1"></i>Waktu Pengajuan
                                            </div>
                                            <div class="info-value">{{ $request->created_at->format('d F Y') }}</div>
                                            <small class="text-muted">{{ $request->created_at->format('H:i') }} WIB</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <div class="info-label">
                                                <i class="bi bi-tag me-1"></i>Jenis Absen
                                            </div>
                                            @php
                                                $jenisColors = [
                                                    'masuk' => 'success',
                                                    'keluar' => 'warning',
                                                    'masuk_keluar' => 'info'
                                                ];
                                                $color = $jenisColors[$request->jenis_absen] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $color }} px-3 py-2 fw-medium">{{ $request->jenis_absen_text }}</span>
                                        </div>
                                    </div>
                                    @if($request->jam_masuk || $request->jam_keluar)
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <div class="info-label">
                                                    <i class="bi bi-clock-history me-1"></i>Estimasi Jam
                                                </div>
                                                @if($request->jam_masuk)
                                                    <div class="d-flex align-items-center mb-1">
                                                        <i class="bi bi-box-arrow-in-right me-2 text-success"></i>
                                                        <span class="fw-medium">Masuk: {{ $request->jam_masuk }}</span>
                                                    </div>
                                                @endif
                                                @if($request->jam_keluar)
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-box-arrow-right me-2 text-warning"></i>
                                                        <span class="fw-medium">Keluar: {{ $request->jam_keluar }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Reason -->
                                <div class="mb-4">
                                    <div class="info-label">
                                        <i class="bi bi-chat-quote me-1"></i>Alasan Pengajuan
                                    </div>
                                    <div class="reason-box">
                                        {{ $request->alasan }}
                                    </div>
                                </div>

                                @if($request->processed_at)
                                    <hr class="my-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="bi bi-gear-fill text-success me-3" style="font-size: 1.5rem;"></i>
                                        <h6 class="mb-0 fw-bold text-dark">Informasi Pemrosesan</h6>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <div class="info-label">
                                                    <i class="bi bi-person-check me-1"></i>Diproses Oleh
                                                </div>
                                                <div class="info-value">{{ $request->processedBy->name ?? 'N/A' }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-item">
                                                <div class="info-label">
                                                    <i class="bi bi-calendar-check me-1"></i>Tanggal Diproses
                                                </div>
                                                <div class="info-value">{{ $request->processed_at->format('d F Y') }}</div>
                                                <small class="text-muted">{{ $request->processed_at->format('H:i') }} WIB</small>
                                            </div>
                                        </div>
                                    </div>

                                    @if($request->admin_notes)
                                        <div class="info-item">
                                            <div class="info-label">
                                                <i class="bi bi-sticky me-1"></i>Catatan Admin
                                            </div>
                                            <div class="reason-box">
                                                {{ $request->admin_notes }}
                                            </div>
                                        </div>
                                    @endif
                                @endif
                            </div>
                        </div>

                        <!-- Action Panel -->
                        <div class="col-lg-4">
                            @if($request->status == 'pending')
                                <!-- Approve Form -->
                                <div class="action-card mb-4 p-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="bi bi-check-circle-fill text-success me-3" style="font-size: 1.5rem;"></i>
                                        <h6 class="mb-0 fw-bold text-white">Setujui Pengajuan</h6>
                                    </div>
                                    <form method="POST" action="{{ route('admin.manual.attendance.requests.approve', $request->id) }}">
                                        @csrf
                                        <div class="mb-3">
                                            <label for="admin_notes_approve" class="form-label text-white-50">Catatan (Opsional)</label>
                                            <textarea class="form-control" id="admin_notes_approve" name="admin_notes" rows="3"
                                                      placeholder="Tambahkan catatan jika diperlukan..."
                                                      style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-action btn-approve w-100" onclick="return confirm('Yakin ingin menyetujui pengajuan ini?')">
                                            <i class="bi bi-check-circle me-2"></i>Setujui Pengajuan
                                        </button>
                                    </form>
                                </div>

                                <!-- Reject Form -->
                                <div class="action-card mb-4 p-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="bi bi-x-circle-fill text-danger me-3" style="font-size: 1.5rem;"></i>
                                        <h6 class="mb-0 fw-bold text-white">Tolak Pengajuan</h6>
                                    </div>
                                    <form method="POST" action="{{ route('admin.manual.attendance.requests.reject', $request->id) }}">
                                        @csrf
                                        <div class="mb-3">
                                            <label for="admin_notes_reject" class="form-label text-white-50">
                                                Alasan Penolakan <span class="text-danger">*</span>
                                            </label>
                                            <textarea class="form-control" id="admin_notes_reject" name="admin_notes" rows="3"
                                                      placeholder="Jelaskan alasan penolakan..." required
                                                      style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-action btn-reject w-100" onclick="return confirm('Yakin ingin menolak pengajuan ini?')">
                                            <i class="bi bi-x-circle me-2"></i>Tolak Pengajuan
                                        </button>
                                    </form>
                                </div>
                            @else
                                <!-- Status Info -->
                                <div class="detail-card p-4 text-center">
                                    @if($request->status == 'approved')
                                        <div class="mb-4">
                                            <div class="mx-auto mb-3" style="width: 80px; height: 80px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="bi bi-check-circle-fill text-white" style="font-size: 2.5rem;"></i>
                                            </div>
                                            <h5 class="text-success fw-bold mb-2">Pengajuan Disetujui</h5>
                                            <p class="text-muted mb-0">Absensi telah dibuat berdasarkan pengajuan ini dan data sudah tersimpan dalam sistem.</p>
                                        </div>
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="bi bi-info-circle me-2 text-info"></i>
                                            <small class="text-muted">Status: Selesai diproses</small>
                                        </div>
                                    @else
                                        <div class="mb-4">
                                            <div class="mx-auto mb-3" style="width: 80px; height: 80px; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="bi bi-x-circle-fill text-white" style="font-size: 2.5rem;"></i>
                                            </div>
                                            <h5 class="text-danger fw-bold mb-2">Pengajuan Ditolak</h5>
                                            <p class="text-muted mb-0">Pengajuan tidak dapat diproses. Silakan lihat catatan admin untuk detail lebih lanjut.</p>
                                        </div>
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="bi bi-exclamation-triangle me-2 text-warning"></i>
                                            <small class="text-muted">Status: Ditolak</small>
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
