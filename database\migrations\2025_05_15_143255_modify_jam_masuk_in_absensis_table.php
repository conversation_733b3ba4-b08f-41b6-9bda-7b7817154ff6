<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            // Ubah kolom jam_masuk agar mengizinkan nilai NULL
            $table->time('jam_masuk')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            // Kembalikan kolom jam_masuk ke NOT NULL
            $table->time('jam_masuk')->nullable(false)->change();
        });
    }
};
